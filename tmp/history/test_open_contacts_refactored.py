"""
Ella语音助手联系人命令测试 - 使用重构后的页面类
测试通过Ella输入"open contacts"命令并验证结果
"""
import pytest
import allure
import time
from pages.apps.ella.dialogue_page import EllaDialoguePage
from core.logger import log
from pages.base.app_detector import AppDetector, AppType


@allure.feature("Ella语音助手")
@allure.story("联系人控制命令 - 重构版本")
class TestEllaContactsCommandRefactored:
    """Ella联系人命令测试类 - 重构版本"""
    
    @pytest.fixture(scope="function")
    def ella_app(self):
        """Ella应用fixture - 使用重构后的页面类"""
        ella_page = EllaDialoguePage()

        try:
            log.info("🚀 开始启动Ella应用（重构版本）...")

            # 启动应用
            if ella_page.start_app():
                log.info("✅ Ella应用启动成功")

                # 等待页面加载
                log.info("⏳ 等待Ella页面加载...")
                if ella_page.wait_for_page_load(timeout=15):
                    log.info("✅ Ella页面加载完成")

                    # 截图记录启动成功状态
                    screenshot_path = ella_page.screenshot("ella_app_started_refactored.png")
                    log.info(f"📸 启动成功截图: {screenshot_path}")

                    yield ella_page
                else:
                    log.error("❌ Ella页面加载失败")
                    screenshot_path = ella_page.screenshot("ella_page_load_failed_refactored.png")
                    log.error(f"📸 页面加载失败截图: {screenshot_path}")
                    pytest.fail("Ella页面加载失败")
            else:
                log.error("❌ Ella应用启动失败")
                pytest.fail("Ella应用启动失败")

        except Exception as e:
            log.error(f"❌ Ella应用fixture异常: {e}")
            try:
                screenshot_path = ella_page.screenshot("ella_fixture_error_refactored.png")
                log.error(f"📸 异常状态截图: {screenshot_path}")
            except:
                pass
            pytest.fail(f"Ella应用fixture异常: {e}")

        finally:
            # 清理：停止应用
            try:
                log.info("🧹 清理Ella应用...")
                ella_page.stop_app()
                log.info("✅ Ella应用已停止")
            except Exception as e:
                log.warning(f"⚠️ 停止Ella应用时出现异常: {e}")
    
    @allure.title("测试open contacts命令 - 重构版本")
    @allure.description("通过重构后的Ella页面类输入'open contacts'命令，验证响应和联系人应用状态")
    @allure.severity(allure.severity_level.CRITICAL)
    @pytest.mark.smoke
    def test_open_contacts_command_refactored(self, ella_app):
        """测试open contacts命令 - 重构版本"""
        command = "open contacts"
        
        with allure.step("记录测试开始状态"):
            # 截图记录初始状态
            screenshot_path = ella_app.screenshot("ella_initial_state_refactored.png")
            allure.attach.file(screenshot_path, name="Ella初始状态（重构版本）", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 记录联系人应用初始状态
            initial_contacts_status = ella_app.check_app_opened(AppType.CONTACTS)
            log.info(f"联系人应用初始状态: {'已打开' if initial_contacts_status else '未打开'}")
            allure.attach(
                f"联系人应用初始状态: {'已打开' if initial_contacts_status else '未打开'}",
                name="联系人应用初始状态",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("确保在对话页面并准备输入"):
            # 确保当前在对话页面
            chat_page_ready = ella_app.ensure_on_chat_page()
            assert chat_page_ready, "无法确保在对话页面"

            # 确保输入框就绪
            input_ready = ella_app.ensure_input_box_ready()
            assert input_ready, "输入框未就绪"

            allure.attach("页面和输入框状态: 就绪", name="预备状态检查",
                         attachment_type=allure.attachment_type.TEXT)

        with allure.step(f"输入命令: {command}"):
            # 执行文本命令
            success = ella_app.execute_text_command(command)
            assert success, f"执行命令失败: {command}"
            
            # 截图记录命令输入后的状态
            screenshot_path = ella_app.screenshot("ella_command_sent_refactored.png")
            allure.attach.file(screenshot_path, name="命令发送后（重构版本）", 
                             attachment_type=allure.attachment_type.PNG)
            
            log.info(f"✅ 成功执行命令: {command}")
        
        with allure.step("等待AI响应"):
            # 等待AI响应
            response_received = ella_app.wait_for_response(timeout=8)

            if not response_received:
                log.warning("⚠️ wait_for_response超时，尝试直接获取响应文本")
                # 等待一段时间让响应出现
                time.sleep(5)

                # 尝试直接获取响应文本
                response_text_check = ella_app.get_response_text_smart()
                if response_text_check:
                    log.info(f"✅ 通过直接获取找到响应: {response_text_check}")
                    response_received = True
                else:
                    log.warning("⚠️ 未收到AI响应，但继续测试")

            if response_received:
                # 等待额外时间确保响应完整
                time.sleep(3)

                # 截图记录响应状态
                screenshot_path = ella_app.screenshot("ella_response_received_refactored.png")
                allure.attach.file(screenshot_path, name="收到AI响应（重构版本）",
                                 attachment_type=allure.attachment_type.PNG)

                log.info("✅ 收到AI响应")
            else:
                # 截图记录无响应状态
                screenshot_path = ella_app.screenshot("ella_no_response_debug_refactored.png")
                allure.attach.file(screenshot_path, name="无响应调试截图（重构版本）",
                                 attachment_type=allure.attachment_type.PNG)

                # 获取页面文本快照用于调试
                debug_snapshot = ella_app._get_page_text_snapshot()
                allure.attach(debug_snapshot, name="页面文本快照",
                             attachment_type=allure.attachment_type.TEXT)

                log.warning("⚠️ 未检测到AI响应，继续测试以验证联系人应用状态")
        
        with allure.step("获取并验证响应内容"):
            # 使用智能方法获取响应文本
            response_text = ella_app.get_response_text_smart()

            if not response_text:
                log.warning("⚠️ 智能方法未获取到响应文本，尝试普通方法")
                response_text = ella_app.get_response_text()

            if not response_text:
                log.warning("⚠️ 普通方法也未获取到响应文本，尝试等待后再次获取")
                time.sleep(3)
                response_text = ella_app.get_response_text_smart()

            # 记录响应文本
            log.info(f"AI响应内容: '{response_text}'")
            allure.attach(f"响应文本: '{response_text}'", name="AI响应内容",
                         attachment_type=allure.attachment_type.TEXT)

            # 验证响应内容
            if response_text:
                command_in_response = ella_app.verify_command_in_response(command, response_text)
                if command_in_response:
                    log.info(f"✅ 响应包含命令相关内容: {command}")
                else:
                    log.warning(f"⚠️ 响应未包含命令相关内容，但继续测试: {command}")
            else:
                log.warning("⚠️ 响应文本为空，跳过内容验证")
        
        with allure.step("验证联系人应用状态"):
            # 等待联系人应用可能的启动
            time.sleep(3)
            
            # 使用智能方法检查联系人应用最终状态
            final_contacts_status = ella_app.check_contacts_app_opened_smart()
            log.info(f"联系人应用最终状态: {'已打开' if final_contacts_status else '未打开'}")
            
            allure.attach(
                f"联系人应用最终状态: {'已打开' if final_contacts_status else '未打开'}",
                name="联系人应用最终状态",
                attachment_type=allure.attachment_type.TEXT
            )
            
            # 验证联系人应用是否已打开
            assert final_contacts_status, "联系人应用未打开"
            
            log.info("✅ 联系人应用已成功打开")
        
        with allure.step("记录测试完成状态"):
            # 最终截图
            screenshot_path = ella_app.screenshot("ella_test_completed_refactored.png")
            allure.attach.file(screenshot_path, name="测试完成状态（重构版本）", 
                             attachment_type=allure.attachment_type.PNG)
            
            # 总结测试结果
            test_summary = f"""
测试命令: {command}
响应内容: {response_text}
联系人应用初始状态: {'已打开' if initial_contacts_status else '未打开'}
联系人应用最终状态: {'已打开' if final_contacts_status else '未打开'}
状态变化: {'是' if initial_contacts_status != final_contacts_status else '否'}
测试结果: 成功
使用版本: 重构版本
模块化设计: ✅ 状态检查器、应用检测器、响应处理器、命令执行器
"""
            allure.attach(test_summary, name="测试总结", 
                         attachment_type=allure.attachment_type.TEXT)
            
            log.info("🎉 open contacts命令测试完成（重构版本）")
    
    @allure.title("测试模块化功能 - 验证重构效果")
    @allure.description("验证重构后的模块化设计是否正常工作")
    @allure.severity(allure.severity_level.NORMAL)
    @pytest.mark.smoke
    def test_modular_functionality(self, ella_app):
        """测试模块化功能"""
        
        with allure.step("验证状态检查器模块"):
            # 测试状态检查器
            bluetooth_status = ella_app.status_checker.check_bluetooth_status()
            ella_process_status = ella_app.status_checker.ensure_ella_process()
            service_health = ella_app.status_checker.check_service_health()
            
            log.info(f"蓝牙状态: {bluetooth_status}")
            log.info(f"Ella进程状态: {ella_process_status}")
            log.info(f"服务健康状态: {service_health}")
            
            allure.attach(
                f"状态检查器测试结果:\n蓝牙状态: {bluetooth_status}\nElla进程: {ella_process_status}\n服务健康: {service_health}",
                name="状态检查器测试",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("验证应用检测器模块"):
            # 测试应用检测器
            contacts_app = ella_app.app_detector.check_app_opened(AppType.CONTACTS)
            weather_app = ella_app.app_detector.check_app_opened(AppType.WEATHER)
            camera_app = ella_app.app_detector.check_app_opened(AppType.CAMERA)
            
            log.info(f"联系人应用状态: {contacts_app}")
            log.info(f"天气应用状态: {weather_app}")
            log.info(f"相机应用状态: {camera_app}")
            
            allure.attach(
                f"应用检测器测试结果:\n联系人应用: {contacts_app}\n天气应用: {weather_app}\n相机应用: {camera_app}",
                name="应用检测器测试",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("验证响应处理器模块"):
            # 测试响应处理器
            page_snapshot = ella_app.response_handler._get_page_text_snapshot()
            element_count = ella_app.response_handler._get_quick_element_count()
            
            log.info(f"页面快照长度: {len(page_snapshot)}")
            log.info(f"页面元素数量: {element_count}")
            
            allure.attach(
                f"响应处理器测试结果:\n页面快照长度: {len(page_snapshot)}\n元素数量: {element_count}",
                name="响应处理器测试",
                attachment_type=allure.attachment_type.TEXT
            )
        
        with allure.step("验证命令执行器模块"):
            # 测试命令执行器
            input_ready = ella_app.command_executor._ensure_input_box_ready()
            
            log.info(f"输入框就绪状态: {input_ready}")
            
            allure.attach(
                f"命令执行器测试结果:\n输入框就绪: {input_ready}",
                name="命令执行器测试",
                attachment_type=allure.attachment_type.TEXT
            )
        
        log.info("✅ 模块化功能验证完成")
        
        # 截图记录模块化测试完成状态
        screenshot_path = ella_app.screenshot("modular_test_completed.png")
        allure.attach.file(screenshot_path, name="模块化测试完成", 
                         attachment_type=allure.attachment_type.PNG)
