"""
批量生成检测器脚本
为Excel表格中的所有Component生成对应的检测器文件
"""
import os
import pandas as pd
from pathlib import Path

# 项目根目录
project_root = Path(__file__).parent.parent
detectors_dir = project_root / "pages" / "base" / "detectors"

# Component到AppType的映射（需要转换为合法的Python标识符）
component_mapping = {
    "Questionnaire": ("questionnaire", "QuestionnaireDetector"),
    "Driving Mode": ("driving_mode", "DrivingModeDetector"),
    "Contact": ("contacts", "ContactsDetector"),  # 已存在，跳过
    "Dialer": ("dialer", "DialerDetector"),  # 已创建
    "SmartMessage": ("smart_message", "SmartMessageDetector"),
    "GoogleContact": ("google_contact", "GoogleContactDetector"),
    "GooglePhone": ("google_phone", "GooglePhoneDetector"),
    "GoogleMessage": ("google_message", "GoogleMessageDetector"),
    "TransID": ("trans_id", "TransIdDetector"),
    "TPMS": ("tpms", "TpmsDetector"),
    "Finder": ("finder", "FinderDetector"),
    "SmartTouch": ("smart_touch", "SmartTouchDetector"),
    "TranssionTips": ("transsion_tips", "TranssionTipsDetector"),
    "Recorder": ("recorder", "RecorderDetector"),
    "Phone Master": ("phone_master", "PhoneMasterDetector"),
    "Ella": ("ella", "EllaDetector"),  # 已创建
    "MOL": ("mol", "MolDetector"),
    "AICoreModelManager": ("ai_core_model_manager", "AiCoreModelManagerDetector"),
    "Ella识屏": ("ella_screen_recognition", "EllaScreenRecognitionDetector"),
    "DemoMode": ("demo_mode", "DemoModeDetector"),
    "AI Gallery": ("ai_gallery", "AiGalleryDetector"),
    "Clipper": ("clipper", "ClipperDetector"),
    "Vishaplayer": ("vishaplayer", "VishaplayerDetector"),  # 已创建
    "Flipmusic": ("flipmusic", "FlipmusicDetector"),
    "Notes": ("notes", "NotesDetector"),
    "Smart Switch": ("smart_switch", "SmartSwitchDetector"),
    "FileManager": ("file_manager", "FileManagerDetector"),  # 已创建
    "Weather": ("weather", "WeatherDetector"),  # 已存在，跳过
    "Clock": ("clock", "ClockDetector"),  # 已存在，跳过
    "Calendar": ("calendar", "CalendarDetector"),
    "FMRadio": ("fm_radio", "FmRadioDetector"),
    "SmartScanner": ("smart_scanner", "SmartScannerDetector"),
    "CommonControl": ("common_control", "CommonControlDetector"),
    "Personalization": ("personalization", "PersonalizationDetector"),
    "Sharevia": ("sharevia", "ShareviaDetector"),
    "One-handed Mode": ("one_handed_mode", "OneHandedModeDetector"),
    "SplitScreen": ("split_screen", "SplitScreenDetector"),
    "Google PIP": ("google_pip", "GooglePipDetector"),
    "Thunderback": ("thunderback", "ThunderbackDetector"),
    "Folding Screen Zone": ("folding_screen_zone", "FoldingScreenZoneDetector"),
    "Smart Panel": ("smart_panel", "SmartPanelDetector"),
    "Smart Hub": ("smart_hub", "SmartHubDetector"),
    "TranssionStylus": ("transsion_stylus", "TranssionStylusDetector"),
    "One-Tap Button": ("one_tap_button", "OneTapButtonDetector"),
    "MicroIntelligence": ("micro_intelligence", "MicroIntelligenceDetector"),
    "Settings": ("settings", "SettingsDetector"),  # 已存在，跳过
    "Google Default": ("google_default", "GoogleDefaultDetector"),
    "FW_DualSystem": ("fw_dual_system", "FwDualSystemDetector"),
    "Multi User": ("multi_user", "MultiUserDetector"),
    "Childmode": ("childmode", "ChildmodeDetector"),
    "Privacy": ("privacy", "PrivacyDetector"),
    "SalesStatistics": ("sales_statistics", "SalesStatisticsDetector"),
    "Headsetcontrol": ("headset_control", "HeadsetControlDetector"),
    "Light Effects": ("light_effects", "LightEffectsDetector"),
    "Removable special": ("removable_special", "RemovableSpecialDetector"),
    "Recent": ("recent", "RecentDetector"),
    "Wallpaper": ("wallpaper", "WallpaperDetector"),
    "MagazineService": ("magazine_service", "MagazineServiceDetector"),
    "Launcher": ("launcher", "LauncherDetector"),  # 已创建
    "Theme": ("theme", "ThemeDetector"),
    "GlobalSearch": ("global_search", "GlobalSearchDetector"),
    "ZeroScreen": ("zero_screen", "ZeroScreenDetector"),
    "Smart Assistant": ("smart_assistant", "SmartAssistantDetector"),
    "CutePet": ("cute_pet", "CutePetDetector"),
    "GlobalTheme": ("global_theme", "GlobalThemeDetector"),
    "SystemUI": ("system_ui", "SystemUiDetector"),
    "OSSettingsExt": ("os_settings_ext", "OsSettingsExtDetector"),
    "ScreenRecord": ("screen_record", "ScreenRecordDetector"),
    "ScreenShot": ("screen_shot", "ScreenShotDetector"),
    "DynamicBar": ("dynamic_bar", "DynamicBarDetector"),
    "FlipExternalScreen": ("flip_external_screen", "FlipExternalScreenDetector"),
    "AOD": ("aod", "AodDetector"),
    "OOBE": ("oobe", "OobeDetector"),
    "Leathercase": ("leathercase", "LeathercaseDetector"),
}

# 已存在的检测器文件（跳过生成）
existing_detectors = {
    "weather_detector.py", "camera_detector.py", "settings_detector.py",
    "contacts_detector.py", "facebook_detector.py", "music_detector.py",
    "clock_detector.py", "maps_detector.py", "playstore_detector.py",
    "alarm_detector.py", "detector_utils.py", "__init__.py", "README.md",
    "fix_imports.py", "ella_detector.py", "dialer_detector.py",
    "file_manager_detector.py", "launcher_detector.py", "vishaplayer_detector.py"
}

def generate_detector_template(component_name, app_type_value, class_name):
    """生成检测器模板代码"""
    # 生成包名列表（基于常见的包名模式）
    package_names = [
        f"com.transsion.{app_type_value.replace('_', '')}",
        f"com.android.{app_type_value.replace('_', '')}",
        f"com.sh.smart.{app_type_value.replace('_', '')}",
    ]
    
    # 生成关键词列表
    keywords = [
        app_type_value.replace('_', ''),
        component_name.lower().replace(' ', ''),
        component_name.lower(),
    ]
    
    # 移除重复项
    package_names = list(dict.fromkeys(package_names))
    keywords = list(dict.fromkeys(keywords))
    
    template = f'''"""
{component_name}检测器
{component_name}应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class {class_name}(BaseAppDetector):
    """{component_name}应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.{app_type_value.upper()})
    
    def get_package_names(self) -> List[str]:
        """获取{component_name}应用包名列表"""
        return {package_names}
    
    def get_keywords(self) -> List[str]:
        """获取{component_name}应用关键词列表"""
        return {keywords}
'''
    return template

def main():
    """主函数"""
    print("开始生成检测器文件...")
    
    generated_count = 0
    skipped_count = 0
    
    for component_name, (app_type_value, class_name) in component_mapping.items():
        filename = f"{app_type_value}_detector.py"
        filepath = detectors_dir / filename
        
        # 跳过已存在的文件
        if filename in existing_detectors:
            print(f"跳过已存在的文件: {filename}")
            skipped_count += 1
            continue
        
        # 生成检测器代码
        detector_code = generate_detector_template(component_name, app_type_value, class_name)
        
        # 写入文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(detector_code)
            print(f"✅ 生成检测器: {filename}")
            generated_count += 1
        except Exception as e:
            print(f"❌ 生成检测器失败 {filename}: {e}")
    
    print(f"\n生成完成！")
    print(f"✅ 新生成: {generated_count} 个检测器")
    print(f"⏭️ 跳过已存在: {skipped_count} 个检测器")

if __name__ == "__main__":
    main()
