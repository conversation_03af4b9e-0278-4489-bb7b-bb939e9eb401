"""
Ella语音助手对话页面
专注于页面元素定义和基本页面操作，其他功能委托给专门的处理器
"""
import sys
import os
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, project_root)

from pages.base.common_page import CommonPage
from pages.base.system_status_checker import SystemStatusChecker
from pages.base.app_detector import AppDetector, AppType
from pages.apps.ella.ella_response_handler import <PERSON>ResponseHandler
from pages.apps.ella.ella_command_executor import EllaCommandExecutor
from core.logger import log


class EllaDialoguePage(CommonPage):
    """Ella语音助手对话页面"""
    
    def __init__(self):
        """初始化Ella对话页面"""
        super().__init__("ella", "dialogue_page")
        
        # 初始化页面元素
        self._init_elements()
        
        # 初始化功能模块
        self._init_modules()
    
    def _init_elements(self):
        """初始化页面元素 - 基于实际脚本的元素ID"""
        # 主要输入框
        self.input_box = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/et_input"},
            "输入框"
        )
        
        # 备选输入框定位
        self.text_input_box = self.create_element(
            {"className": "android.widget.EditText"},
            "文本输入框(备选)"
        )
        
        # 发送按钮
        self.send_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/fl_btn_three_btn"},
            "发送按钮"
        )
        
        # 语音输入按钮
        self.voice_input_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_voice"},
            "语音输入按钮"
        )
        
        # 备选语音按钮
        self.voice_button_alt = self.create_element(
            {"className": "android.widget.ImageView", "description": "语音输入"},
            "语音按钮(备选)"
        )
        
        # TTS播放按钮
        self.tts_play_button = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/iv_tts_play"},
            "TTS播放按钮"
        )
        
        # 聊天列表
        self.chat_list = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/rv_chat"},
            "聊天列表"
        )
        
        # Ella问候语
        self.ella_greeting = self.create_element(
            {"text": "Hi, I'm Ella. How can I help you?"},
            "Ella问候语"
        )
        
        # 通用文本视图
        self.text_view_generic = self.create_element(
            {"className": "android.widget.TextView"},
            "通用文本视图"
        )
        
        # 响应文本元素
        self.response_text = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_response"},
            "响应文本"
        )
        
        # 功能控制元素
        self.function_control = self.create_element(
            {"resourceId": "com.transsion.aivoiceassistant:id/tv_function_control"},
            "功能控制"
        )
    
    def _init_modules(self):
        """初始化功能模块"""
        # 页面元素字典，供其他模块使用
        self.page_elements = {
            'input_box': self.input_box,
            'text_input_box': self.text_input_box,
            'send_button': self.send_button,
            'voice_input_button': self.voice_input_button,
            'voice_button_alt': self.voice_button_alt,
            'tts_play_button': self.tts_play_button,
            'chat_list': self.chat_list,
            'ella_greeting': self.ella_greeting,
            'text_view_generic': self.text_view_generic
        }
        
        # 初始化功能模块
        self.status_checker = SystemStatusChecker(self.driver)
        self.app_detector = AppDetector()
        self.response_handler = EllaResponseHandler(self.driver, self.status_checker)
        self.command_executor = EllaCommandExecutor(self.driver, self.page_elements)
    
    # ==================== 应用启动和页面管理 ====================
    
    def start_app(self) -> bool:
        """启动Ella应用"""
        try:
            log.info("启动Ella应用")

            package_name = "com.transsion.aivoiceassistant"
            activity_name = "com.transsion.ella.ai.aiability.help.activity.HelpMainLargeActivity"

            # 方法1: 尝试启动指定Activity
            try:
                self.driver.app_start(package_name, activity_name)
                log.info(f"尝试启动Activity: {activity_name}")
                time.sleep(3)

                # 检查应用是否启动成功
                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（指定Activity）")
                    return True
            except Exception as e:
                log.warning(f"指定Activity启动失败: {e}")

            # 方法2: 备选方案：使用默认启动方式
            try:
                self.driver.app_start(package_name)
                log.info("尝试默认方式启动应用")
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（默认方式）")
                    return True
            except Exception as e:
                log.warning(f"默认启动方式失败: {e}")

            # 方法3: 使用shell命令启动
            try:
                log.info("尝试使用shell命令启动")
                cmd = f"am start -n {package_name}/{activity_name}"
                self.driver.shell(cmd)
                time.sleep(3)

                if self._check_app_started(package_name):
                    log.info("✅ Ella应用启动成功（shell命令）")
                    return True
            except Exception as e:
                log.warning(f"shell命令启动失败: {e}")

            log.error("❌ 所有启动方法都失败")
            return False

        except Exception as e:
            log.error(f"启动Ella应用异常: {e}")
            return False

    def _check_app_started(self, package_name: str) -> bool:
        """
        检查应用是否启动成功

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否启动成功
        """
        try:
            # 方法1: 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')

            if current_package == package_name:
                log.info(f"✅ 应用已在前台: {current_package}")
                return True

            # 方法2: 检查应用是否在运行
            running_apps = self.driver.app_list_running()
            if package_name in running_apps:
                log.info(f"✅ 应用正在运行: {package_name}")
                # 尝试切换到前台
                self.driver.app_start(package_name)
                time.sleep(1)
                return True

            return False

        except Exception as e:
            log.error(f"检查应用启动状态失败: {e}")
            return False
    
    def wait_for_page_load(self, timeout: int = 15) -> bool:
        """等待页面加载完成"""
        try:
            log.info(f"等待Ella页面加载完成 (超时: {timeout}秒)")

            # 等待输入框出现
            if self.input_box.wait_for_element(timeout=timeout):
                log.info("✅ 输入框已出现，页面加载完成")
                return True
            elif self.text_input_box.wait_for_element(timeout=5):
                log.info("✅ 备选输入框已出现，页面加载完成")
                return True
            else:
                log.error("❌ 页面加载超时，未找到输入框")
                return False

        except Exception as e:
            log.error(f"等待页面加载异常: {e}")
            return False
    
    def stop_app(self) -> bool:
        """停止Ella应用"""
        try:
            log.info("停止Ella应用")
            package_name = "com.transsion.aivoiceassistant"

            # app_stop方法不返回值，直接执行操作
            self.driver.app_stop(package_name)

            # 等待一下让应用完全停止
            time.sleep(1)

            # 验证应用是否真的停止了
            if self._verify_app_stopped(package_name):
                log.info("✅ Ella应用已成功停止")
                return True
            else:
                log.info("✅ Ella应用停止命令已执行")
                return True  # 即使验证失败，也认为停止命令执行成功

        except Exception as e:
            log.error(f"停止Ella应用异常: {e}")
            return False

    def _verify_app_stopped(self, package_name: str) -> bool:
        """
        验证应用是否已停止

        Args:
            package_name: 应用包名

        Returns:
            bool: 应用是否已停止
        """
        try:
            # 检查当前前台应用
            current_app = self.driver.app_current()
            current_package = current_app.get('package', '')

            # 如果当前前台应用不是目标应用，说明已经停止或切换了
            if current_package != package_name:
                log.debug(f"当前前台应用: {current_package}，目标应用已不在前台")
                return True

            # 检查应用是否还在运行列表中
            running_apps = self.driver.app_list_running()
            if package_name not in running_apps:
                log.debug(f"应用不在运行列表中: {package_name}")
                return True

            # 如果应用仍在运行，但不在前台，也认为停止成功
            log.debug(f"应用可能仍在后台运行: {package_name}")
            return True

        except Exception as e:
            log.debug(f"验证应用停止状态失败: {e}")
            # 验证失败时，保守地认为停止成功
            return True
    
    def return_to_ella_app(self) -> bool:
        """返回到Ella应用"""
        try:
            log.info("尝试返回Ella应用")
            
            # 方法1: 直接启动Ella应用
            if self.start_app():
                if self.wait_for_page_load(timeout=10):
                    log.info("✅ 成功返回Ella应用")
                    return True
            
            # 方法2: 使用最近任务切换
            log.info("尝试通过最近任务返回Ella")
            self.driver.press("recent")
            time.sleep(1)
            
            # 查找Ella应用并点击
            ella_app = self.driver(text="Ella", className="android.widget.TextView")
            if ella_app.exists(timeout=3):
                ella_app.click()
                time.sleep(2)
                if self.wait_for_page_load(timeout=10):
                    log.info("✅ 通过最近任务成功返回Ella应用")
                    return True
            
            log.error("❌ 无法返回Ella应用")
            return False
            
        except Exception as e:
            log.error(f"返回Ella应用异常: {e}")
            return False
    
    # ==================== 命令执行方法 ====================
    
    def execute_text_command(self, command: str) -> bool:
        """执行文本命令"""
        return self.command_executor.execute_text_command(command)
    
    def execute_voice_command(self, command: str, duration: float = 3.0, language: str = "en") -> bool:
        """执行语音命令"""
        return self.command_executor.execute_voice_command(command, duration, language)
    
    # ==================== 响应处理方法 ====================
    
    def wait_for_response(self, timeout: int = 10) -> bool:
        """等待AI响应"""
        return self.response_handler.wait_for_response(timeout)
    
    def get_response_text(self) -> str:
        """获取响应文本"""
        return self.response_handler.get_response_text()
    
    def verify_command_in_response(self, command: str, response: str) -> bool:
        """验证响应中是否包含命令相关内容"""
        return self.response_handler.verify_command_in_response(command, response)
    
    # ==================== 状态检查方法 ====================
    
    def check_bluetooth_status(self) -> bool:
        """检查蓝牙状态"""
        return self.status_checker.check_bluetooth_status()
    
    def check_wifi_status(self) -> bool:
        """检查WiFi状态"""
        return self.status_checker.check_wifi_status()
    
    def check_flashlight_status(self) -> bool:
        """检查手电筒状态"""
        return self.status_checker.check_flashlight_status()
    
    def check_alarm_status(self) -> bool:
        """检查闹钟状态"""
        return self.status_checker.check_alarm_status()
    
    # ==================== 应用检测方法 ====================
    
    def check_weather_app_opened(self) -> bool:
        """检查天气应用是否打开"""
        return self.app_detector.check_app_opened(AppType.WEATHER)
    
    def check_camera_app_opened(self) -> bool:
        """检查相机应用是否打开"""
        return self.app_detector.check_app_opened(AppType.CAMERA)
    
    def check_settings_opened(self) -> bool:
        """检查设置应用是否打开"""
        return self.app_detector.check_app_opened(AppType.SETTINGS)
    
    def check_facebook_app_opened(self) -> bool:
        """检查Facebook应用是否打开"""
        return self.app_detector.check_app_opened(AppType.FACEBOOK)
    
    def check_clock_app_opened(self) -> bool:
        """检查时钟应用是否打开"""
        return self.app_detector.check_app_opened(AppType.CLOCK)

    def check_google_map_app_opened(self) -> bool:
        """检查Google地图应用是否打开"""
        return self.app_detector.check_app_opened(AppType.MAPS)
    def check_google_playstore_app_opened(self) -> bool:
        """检查Google地图应用是否打开"""
        return self.app_detector.check_app_opened(AppType.PLAYSTORE)

    def check_visha_app_opened(self)-> bool:
        """检查Visha应用是否打开"""
        return self.app_detector.check_app_opened(AppType.MUSIC)

    # ==================== 智能版本的方法 ====================
    
    def check_bluetooth_status_smart(self) -> bool:
        """智能检查蓝牙状态"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查蓝牙状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False
        return self.status_checker.check_bluetooth_status()
    
    def check_contacts_app_opened_smart(self) -> bool:
        """智能检查联系人应用状态 - 使用优化的检测器"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查联系人应用状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False

        # 使用优化的智能检测方法
        return self.app_detector.check_contacts_app_opened_smart()
    
    def check_contacts_app_opened(self) -> bool:
        """检查联系人应用是否打开 - 基础方法"""
        return self.app_detector.check_app_opened(AppType.CONTACTS)

    def get_response_text_smart(self) -> str:
        """智能获取响应文本"""
        if not self.status_checker.ensure_ella_process():
            log.warning("获取响应文本时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return ""
        return self.response_handler.get_response_text()

    def get_response_all_text(self) -> list:
        """智能获取响应文本"""
        if not self.status_checker.ensure_ella_process():
            log.warning("获取响应文本时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return []
        return self.response_handler.get_response_all_text()

    def check_alarm_status_smart(self) -> bool:
        """智能检查闹钟状态 - 包含进程检测"""
        if not self.status_checker.ensure_ella_process():
            log.warning("检查闹钟状态时不在Ella进程，尝试返回")
            if not self.return_to_ella_app():
                log.error("无法返回Ella应用")
                return False
        return self.app_detector.check_alarm_status()

    def get_alarm_list(self) -> list:
        """获取闹钟列表"""
        return self.app_detector.get_alarm_list()

    def verify_alarm_set(self, target_time: str = "10:00") -> bool:
        """验证闹钟是否设置成功"""
        try:
            log.info(f"验证闹钟设置: {target_time}")

            # 获取闹钟列表
            alarm_list = self.get_alarm_list()

            # 检查目标时间是否在列表中
            for alarm in alarm_list:
                if alarm.get('time') == target_time:
                    log.info(f"✅ 找到目标闹钟: {target_time}")
                    return True

            log.info(f"❌ 未找到目标闹钟: {target_time}")
            return False

        except Exception as e:
            log.error(f"验证闹钟设置失败: {e}")
            return False

    def verify_alarm_in_list(self, target_time: str) -> bool:
        """验证闹钟是否在列表中"""
        return self.verify_alarm_set(target_time)

    def ensure_input_box_ready(self) -> bool:
        """确保输入框就绪"""
        return self.command_executor._ensure_input_box_ready()

    # ==================== 页面状态检查 ====================

    def ensure_on_chat_page(self) -> bool:
        """
        确保当前在对话页面

        Returns:
            bool: 是否成功回到对话页面
        """
        try:
            log.info("确保在对话页面...")

            # 检查当前进程是否是Ella
            if not self.status_checker.ensure_ella_process():
                log.warning("当前不在Ella进程，尝试返回Ella")
                if not self.return_to_ella_app():
                    log.error("无法返回Ella应用")
                    return False

            # 检查是否在对话页面
            if self._check_chat_page_indicators():
                log.info("✅ 已在对话页面")
                return True

            # 尝试返回到对话页面
            if self._try_return_to_chat_page():
                log.info("✅ 成功返回到对话页面")
                return True

            # 宽松策略：如果在Ella应用中，就认为成功
            if self.status_checker.ensure_ella_process():
                log.info("✅ 在Ella应用中，使用宽松策略认为在对话页面")
                return True

            log.error("❌ 无法确保在对话页面")
            return False

        except Exception as e:
            log.error(f"确保在对话页面失败: {e}")
            return False

    def _check_chat_page_indicators(self) -> bool:
        """
        检查对话页面的多种指示器

        Returns:
            bool: 是否在对话页面
        """
        try:
            log.debug("检查对话页面指示器...")

            # 指示器1: 输入框存在
            if self.input_box.is_exists():
                log.debug("✅ 找到主输入框")
                return True

            # 指示器2: 备选输入框存在
            if self.text_input_box.is_exists():
                log.debug("✅ 找到备选输入框")
                return True

            # 指示器3: 聊天列表存在
            if self.chat_list.is_exists():
                log.debug("✅ 找到聊天列表")
                return True

            # 指示器4: 语音按钮存在
            if self.voice_input_button.is_exists():
                log.debug("✅ 找到语音输入按钮")
                return True

            # 指示器5: Ella问候语存在
            if self.ella_greeting.is_exists():
                log.debug("✅ 找到Ella问候语")
                return True

            log.debug("❌ 未找到任何对话页面指示器")
            return False

        except Exception as e:
            log.error(f"检查对话页面指示器失败: {e}")
            return False

    def _try_return_to_chat_page(self) -> bool:
        """
        尝试返回到对话页面

        Returns:
            bool: 是否成功返回
        """
        try:
            log.info("尝试返回到对话页面...")

            # 方法1: 按返回键
            log.debug("尝试按返回键...")
            self.driver.press("back")
            time.sleep(1)

            if self._check_chat_page_indicators():
                log.info("✅ 通过返回键成功回到对话页面")
                return True

            # 方法2: 按Home键然后重新启动应用
            log.debug("尝试重新启动应用...")
            self.driver.press("home")
            time.sleep(1)

            if self.start_app():
                time.sleep(2)
                if self._check_chat_page_indicators():
                    log.info("✅ 通过重新启动成功回到对话页面")
                    return True

            log.warning("❌ 无法返回到对话页面")
            return False

        except Exception as e:
            log.error(f"尝试返回对话页面失败: {e}")
            return False

    # ==================== 兼容性方法 ====================

    def start_app_with_activity(self) -> bool:
        """启动应用（兼容性方法）"""
        return self.start_app()

    def _get_page_text_snapshot(self) -> str:
        """获取页面文本快照（兼容性方法）"""
        return self.response_handler._get_page_text_snapshot()
