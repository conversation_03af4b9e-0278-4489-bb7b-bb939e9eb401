"""
优化后的 EllaResponseHandler 使用示例
展示如何使用新的 get_response_text 方法从 check_area 节点获取响应文本
"""
from pages.apps.ella.ella_response_handler import EllaResponseHandler
from core.logger import log


class ResponseHandlerExample:
    """响应处理器使用示例"""
    
    def __init__(self, driver, status_checker=None):
        """
        初始化示例类

        Args:
            driver: UIAutomator2驱动实例
            status_checker: Ella状态检查器实例（可选）
        """
        self.response_handler = EllaResponseHandler(driver, status_checker)
    
    def example_get_response_text(self):
        """
        获取响应文本的示例
        
        优化后的方法会按以下优先级获取文本：
        1. 从 check_area 节点直接获取
        2. 从 robot_text 子节点获取
        3. 从 TextView 子节点收集
        4. 从页面dump解析获取
        5. 备用方法（原有的获取方式）
        """
        try:
            log.info("=== 开始获取AI响应文本 ===")
            
            # 调用优化后的方法
            response_text = self.response_handler.get_response_text()
            
            if response_text:
                log.info(f"✅ 成功获取响应文本: {response_text}")
                return response_text
            else:
                log.warning("⚠️ 未获取到响应文本")
                return ""
                
        except Exception as e:
            log.error(f"❌ 获取响应文本失败: {e}")
            return ""
    
    def example_wait_and_get_response(self, timeout: int = 10):
        """
        等待并获取响应的完整示例
        
        Args:
            timeout: 等待超时时间
            
        Returns:
            str: 响应文本
        """
        try:
            log.info(f"=== 等待AI响应 (超时: {timeout}秒) ===")
            
            # 等待响应出现
            response_received = self.response_handler.wait_for_response(timeout)
            
            if response_received:
                log.info("✅ 检测到AI响应")
                
                # 获取响应文本
                response_text = self.response_handler.get_response_text()
                
                if response_text:
                    log.info(f"🎉 完整流程成功，响应内容: {response_text}")
                    return response_text
                else:
                    log.warning("⚠️ 检测到响应但未获取到文本内容")
                    return ""
            else:
                log.warning("⚠️ 等待响应超时")
                return ""
                
        except Exception as e:
            log.error(f"❌ 等待并获取响应失败: {e}")
            return ""
    
    def example_verify_command_response(self, command: str, expected_keywords: list):
        """
        验证命令响应的示例
        
        Args:
            command: 执行的命令
            expected_keywords: 期望在响应中出现的关键词列表
            
        Returns:
            bool: 验证是否通过
        """
        try:
            log.info(f"=== 验证命令响应: {command} ===")
            
            # 获取响应文本
            response_text = self.response_handler.get_response_text()
            
            if not response_text:
                log.warning("⚠️ 未获取到响应文本，无法验证")
                return False
            
            # 验证命令相关性
            command_related = self.response_handler.verify_command_in_response(command, response_text)
            
            if not command_related:
                log.warning(f"⚠️ 响应与命令不相关: {command}")
                return False
            
            # 验证期望关键词
            found_keywords = []
            missing_keywords = []
            
            for keyword in expected_keywords:
                if keyword.lower() in response_text.lower():
                    found_keywords.append(keyword)
                    log.info(f"✅ 找到期望关键词: {keyword}")
                else:
                    missing_keywords.append(keyword)
                    log.warning(f"⚠️ 缺失期望关键词: {keyword}")
            
            # 输出验证结果
            if missing_keywords:
                log.warning(f"❌ 验证失败，缺失关键词: {missing_keywords}")
                return False
            else:
                log.info(f"🎉 验证成功，所有关键词都已找到: {found_keywords}")
                return True
                
        except Exception as e:
            log.error(f"❌ 验证命令响应失败: {e}")
            return False


def demo_usage():
    """演示用法"""
    print("=== EllaResponseHandler 优化后的使用示例 ===\n")
    
    print("1. 优化后的 get_response_text 方法特点：")
    print("   - 优先从 check_area 节点获取文本")
    print("   - 支持多种获取方式的降级策略")
    print("   - 更精确地定位AI响应内容")
    print("   - 支持从XML dump解析文本")
    
    print("\n2. 获取响应文本的优先级：")
    print("   ① check_area 节点直接获取")
    print("   ② robot_text 子节点获取")
    print("   ③ TextView 子节点收集")
    print("   ④ 页面dump解析获取")
    print("   ⑤ 备用方法（原有方式）")
    
    print("\n3. 节点结构支持：")
    print("   - resource-id: com.transsion.aivoiceassistant:id/check_area")
    print("   - 子节点: com.transsion.aivoiceassistant:id/robot_text")
    print("   - 支持多层级文本收集")
    
    print("\n4. 使用方法：")
    print("   ```python")
    print("   # 基本用法")
    print("   handler = EllaResponseHandler(driver)")
    print("   # 推荐用法（带状态检查）")
    print("   handler = EllaResponseHandler(driver, status_checker)")
    print("   response = handler.get_response_text()")
    print("   ```")


if __name__ == "__main__":
    demo_usage()
