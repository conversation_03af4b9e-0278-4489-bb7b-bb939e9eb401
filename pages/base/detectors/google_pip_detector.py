"""
Google PIP检测器
Google PIP应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GooglePipDetector(BaseAppDetector):
    """Google PIP应用检测器"""

    def __init__(self):
        super().__init__(AppType.GOOGLE_PIP)

    def get_package_names(self) -> List[str]:
        """获取Google PIP应用包名列表"""
        return ['与systmeUI（com.android.systemui）共用', 'com.transsion.googlepip', 'com.android.googlepip', 'com.sh.smart.googlepip']

    def get_keywords(self) -> List[str]:
        """获取Google PIP应用关键词列表"""
        return ['画中画', 'google pip', 'googlepip', 'pictureinpicture']
