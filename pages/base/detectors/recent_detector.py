"""
Recent检测器
Recent应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class RecentDetector(BaseAppDetector):
    """Recent应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.RECENT)
    
    def get_package_names(self) -> List[str]:
        """获取Recent应用包名列表"""
        return ['com.transsion.recent', 'com.android.recent', 'com.sh.smart.recent']
    
    def get_keywords(self) -> List[str]:
        """获取Recent应用关键词列表"""
        return ['recent']
