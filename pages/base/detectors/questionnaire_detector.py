"""
Questionnaire检测器
Questionnaire应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class QuestionnaireDetector(BaseAppDetector):
    """Questionnaire应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.QUESTIONNAIRE)
    
    def get_package_names(self) -> List[str]:
        """获取Questionnaire应用包名列表"""
        return ['com.transsion.questionnaire', 'com.android.questionnaire', 'com.sh.smart.questionnaire']
    
    def get_keywords(self) -> List[str]:
        """获取Questionnaire应用关键词列表"""
        return ['questionnaire']
