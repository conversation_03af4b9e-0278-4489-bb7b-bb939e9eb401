"""
GoogleContact检测器
GoogleContact应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class GoogleContactDetector(BaseAppDetector):
    """GoogleContact应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.GOOGLE_CONTACT)
    
    def get_package_names(self) -> List[str]:
        """获取GoogleContact应用包名列表"""
        return ['com.transsion.googlecontact', 'com.android.googlecontact', 'com.sh.smart.googlecontact']
    
    def get_keywords(self) -> List[str]:
        """获取GoogleContact应用关键词列表"""
        return ['googlecontact']
