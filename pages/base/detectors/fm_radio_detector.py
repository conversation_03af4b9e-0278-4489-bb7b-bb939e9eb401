"""
FMRadio检测器
FMRadio应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FmRadioDetector(BaseAppDetector):
    """FMRadio应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.FM_RADIO)
    
    def get_package_names(self) -> List[str]:
        """获取FMRadio应用包名列表"""
        return ['com.transsion.fmradio', 'com.android.fmradio', 'com.sh.smart.fmradio']
    
    def get_keywords(self) -> List[str]:
        """获取FMRadio应用关键词列表"""
        return ['fmradio']
