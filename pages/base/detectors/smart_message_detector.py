"""
SmartMessage检测器
SmartMessage应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartMessageDetector(BaseAppDetector):
    """SmartMessage应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SMART_MESSAGE)
    
    def get_package_names(self) -> List[str]:
        """获取SmartMessage应用包名列表"""
        return ['com.transsion.smartmessage', 'com.android.smartmessage', 'com.sh.smart.smartmessage']
    
    def get_keywords(self) -> List[str]:
        """获取SmartMessage应用关键词列表"""
        return ['smartmessage']
