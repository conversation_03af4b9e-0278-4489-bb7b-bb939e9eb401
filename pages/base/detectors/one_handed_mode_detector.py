"""
One-handed Mode检测器
One-handed Mode应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class OneHandedModeDetector(BaseAppDetector):
    """One-handed Mode应用检测器"""

    def __init__(self):
        super().__init__(AppType.ONE_HANDED_MODE)

    def get_package_names(self) -> List[str]:
        """获取One-handed Mode应用包名列表"""
        return ['与systmeUI（com.android.systemui）共用', 'com.transsion.onehandedmode', 'com.android.onehandedmode', 'com.sh.smart.onehandedmode']

    def get_keywords(self) -> List[str]:
        """获取One-handed Mode应用关键词列表"""
        return ['单手模式', 'one-handed mode', 'one-handedmode', 'one-hand mode', 'one-handmode', 'onehandedmode']
