"""
TranssionStylus检测器
TranssionStylus应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class TranssionStylusDetector(BaseAppDetector):
    """TranssionStylus应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.TRANSSION_STYLUS)
    
    def get_package_names(self) -> List[str]:
        """获取TranssionStylus应用包名列表"""
        return ['com.transsion.transsionstylus', 'com.android.transsionstylus', 'com.sh.smart.transsionstylus']
    
    def get_keywords(self) -> List[str]:
        """获取TranssionStylus应用关键词列表"""
        return ['transsionstylus']
