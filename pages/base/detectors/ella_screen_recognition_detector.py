"""
Ella识屏检测器
Ella识屏应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class EllaScreenRecognitionDetector(BaseAppDetector):
    """Ella识屏应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.ELLA_SCREEN_RECOGNITION)
    
    def get_package_names(self) -> List[str]:
        """获取Ella识屏应用包名列表"""
        return ['com.transsion.ellascreenrecognition', 'com.android.ellascreenrecognition', 'com.sh.smart.ellascreenrecognition']
    
    def get_keywords(self) -> List[str]:
        """获取Ella识屏应用关键词列表"""
        return ['ellascreenrecognition', 'ella识屏']
