"""
AICoreModelManager检测器
AICoreModelManager应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class AiCoreModelManagerDetector(BaseAppDetector):
    """AICoreModelManager应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.AI_CORE_MODEL_MANAGER)
    
    def get_package_names(self) -> List[str]:
        """获取AICoreModelManager应用包名列表"""
        return ['com.transsion.aicoremodelmanager', 'com.android.aicoremodelmanager', 'com.sh.smart.aicoremodelmanager']
    
    def get_keywords(self) -> List[str]:
        """获取AICoreModelManager应用关键词列表"""
        return ['aicoremodelmanager']
