"""
Sharevia检测器
Sharevia应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ShareviaDetector(BaseAppDetector):
    """Sharevia应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SHAREVIA)
    
    def get_package_names(self) -> List[str]:
        """获取Sharevia应用包名列表"""
        return ['com.transsion.sharevia', 'com.android.sharevia', 'com.sh.smart.sharevia']
    
    def get_keywords(self) -> List[str]:
        """获取Sharevia应用关键词列表"""
        return ['sharevia']
