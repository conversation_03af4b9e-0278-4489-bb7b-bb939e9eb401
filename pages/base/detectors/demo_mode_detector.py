"""
DemoMode检测器
DemoMode应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class DemoModeDetector(BaseAppDetector):
    """DemoMode应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.DEMO_MODE)
    
    def get_package_names(self) -> List[str]:
        """获取DemoMode应用包名列表"""
        return ['com.transsion.demomode', 'com.android.demomode', 'com.sh.smart.demomode']
    
    def get_keywords(self) -> List[str]:
        """获取DemoMode应用关键词列表"""
        return ['demomode']
