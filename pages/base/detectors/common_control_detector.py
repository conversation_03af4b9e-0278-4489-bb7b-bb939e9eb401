"""
CommonControl检测器
CommonControl应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CommonControlDetector(BaseAppDetector):
    """CommonControl应用检测器"""

    def __init__(self):
        super().__init__(AppType.COMMON_CONTROL)

    def get_package_names(self) -> List[str]:
        """获取CommonControl应用包名列表"""
        return ['与systmeUI（com.android.systemui）共用', 'com.transsion.commoncontrol', 'com.android.commoncontrol', 'com.sh.smart.commoncontrol']

    def get_keywords(self) -> List[str]:
        """获取CommonControl应用关键词列表"""
        return ['公共控件', 'commoncontrol']
