"""
Privacy检测器
Privacy应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class PrivacyDetector(BaseAppDetector):
    """Privacy应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.PRIVACY)
    
    def get_package_names(self) -> List[str]:
        """获取Privacy应用包名列表"""
        return ['com.transsion.privacy', 'com.android.privacy', 'com.sh.smart.privacy']
    
    def get_keywords(self) -> List[str]:
        """获取Privacy应用关键词列表"""
        return ['privacy']
