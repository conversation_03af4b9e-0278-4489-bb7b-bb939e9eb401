"""
Thunderback检测器
Thunderback应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ThunderbackDetector(BaseAppDetector):
    """Thunderback应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.THUNDERBACK)
    
    def get_package_names(self) -> List[str]:
        """获取Thunderback应用包名列表"""
        return ['com.transsion.thunderback', 'com.android.thunderback', 'com.sh.smart.thunderback']
    
    def get_keywords(self) -> List[str]:
        """获取Thunderback应用关键词列表"""
        return ['thunderback']
