"""
MicroIntelligence检测器
MicroIntelligence应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class MicroIntelligenceDetector(BaseAppDetector):
    """MicroIntelligence应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.MICRO_INTELLIGENCE)
    
    def get_package_names(self) -> List[str]:
        """获取MicroIntelligence应用包名列表"""
        return ['com.transsion.microintelligence', 'com.android.microintelligence', 'com.sh.smart.microintelligence']
    
    def get_keywords(self) -> List[str]:
        """获取MicroIntelligence应用关键词列表"""
        return ['microintelligence']
