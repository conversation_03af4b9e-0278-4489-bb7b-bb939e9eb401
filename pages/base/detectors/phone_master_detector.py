"""
Phone Master检测器
Phone Master应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class PhoneMasterDetector(BaseAppDetector):
    """Phone Master应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.PHONE_MASTER)
    
    def get_package_names(self) -> List[str]:
        """获取Phone Master应用包名列表"""
        return ['com.transsion.phonemaster', 'com.android.phonemaster', 'com.sh.smart.phonemaster']
    
    def get_keywords(self) -> List[str]:
        """获取Phone Master应用关键词列表"""
        return ['phonemaster', 'phone master']
