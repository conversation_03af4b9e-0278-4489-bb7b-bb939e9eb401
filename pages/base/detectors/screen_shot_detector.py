"""
ScreenShot检测器
ScreenShot应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ScreenShotDetector(BaseAppDetector):
    """ScreenShot应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SCREEN_SHOT)
    
    def get_package_names(self) -> List[str]:
        """获取ScreenShot应用包名列表"""
        return ['com.transsion.screenshot', 'com.android.screenshot', 'com.sh.smart.screenshot']
    
    def get_keywords(self) -> List[str]:
        """获取ScreenShot应用关键词列表"""
        return ['screenshot']
