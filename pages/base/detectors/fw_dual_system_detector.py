"""
FW_DualSystem检测器
FW_DualSystem应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class FwDualSystemDetector(BaseAppDetector):
    """FW_DualSystem应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.FW_DUAL_SYSTEM)
    
    def get_package_names(self) -> List[str]:
        """获取FW_DualSystem应用包名列表"""
        return ['com.transsion.fwdualsystem', 'com.android.fwdualsystem', 'com.sh.smart.fwdualsystem']
    
    def get_keywords(self) -> List[str]:
        """获取FW_DualSystem应用关键词列表"""
        return ['fwdualsystem', 'fw_dualsystem']
