"""
SmartTouch检测器
SmartTouch应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartTouchDetector(BaseAppDetector):
    """SmartTouch应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SMART_TOUCH)
    
    def get_package_names(self) -> List[str]:
        """获取SmartTouch应用包名列表"""
        return ['com.transsion.smarttouch', 'com.android.smarttouch', 'com.sh.smart.smarttouch']
    
    def get_keywords(self) -> List[str]:
        """获取SmartTouch应用关键词列表"""
        return ['smarttouch']
