"""
Notes检测器
Notes应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class NotesDetector(BaseAppDetector):
    """Notes应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.NOTES)
    
    def get_package_names(self) -> List[str]:
        """获取Notes应用包名列表"""
        return ['com.transsion.notes', 'com.android.notes', 'com.sh.smart.notes']
    
    def get_keywords(self) -> List[str]:
        """获取Notes应用关键词列表"""
        return ['notes']
