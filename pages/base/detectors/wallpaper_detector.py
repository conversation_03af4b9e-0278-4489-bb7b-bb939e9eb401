"""
Wallpaper检测器
Wallpaper应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class WallpaperDetector(BaseAppDetector):
    """Wallpaper应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.WALLPAPER)
    
    def get_package_names(self) -> List[str]:
        """获取Wallpaper应用包名列表"""
        return ['com.transsion.wallpaper', 'com.android.wallpaper', 'com.sh.smart.wallpaper']
    
    def get_keywords(self) -> List[str]:
        """获取Wallpaper应用关键词列表"""
        return ['wallpaper']
