"""
Smart Switch检测器
Smart Switch应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SmartSwitchDetector(BaseAppDetector):
    """Smart Switch应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SMART_SWITCH)
    
    def get_package_names(self) -> List[str]:
        """获取Smart Switch应用包名列表"""
        return ['com.transsion.smartswitch', 'com.android.smartswitch', 'com.sh.smart.smartswitch']
    
    def get_keywords(self) -> List[str]:
        """获取Smart Switch应用关键词列表"""
        return ['smartswitch', 'smart switch']
