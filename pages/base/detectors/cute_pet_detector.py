"""
CutePet检测器
CutePet应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class CutePetDetector(BaseAppDetector):
    """CutePet应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CUTE_PET)
    
    def get_package_names(self) -> List[str]:
        """获取CutePet应用包名列表"""
        return ['com.transsion.cutepet', 'com.android.cutepet', 'com.sh.smart.cutepet']
    
    def get_keywords(self) -> List[str]:
        """获取CutePet应用关键词列表"""
        return ['cutepet']
