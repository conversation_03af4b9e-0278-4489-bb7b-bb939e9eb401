"""
AOD检测器
AOD应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class AodDetector(BaseAppDetector):
    """AOD应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.AOD)
    
    def get_package_names(self) -> List[str]:
        """获取AOD应用包名列表"""
        return ['com.transsion.aod', 'com.android.aod', 'com.sh.smart.aod']
    
    def get_keywords(self) -> List[str]:
        """获取AOD应用关键词列表"""
        return ['aod']
