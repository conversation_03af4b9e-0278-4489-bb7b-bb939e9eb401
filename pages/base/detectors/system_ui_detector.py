"""
SystemUI检测器
SystemUI应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SystemUiDetector(BaseAppDetector):
    """SystemUI应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SYSTEM_UI)
    
    def get_package_names(self) -> List[str]:
        """获取SystemUI应用包名列表"""
        return ['com.transsion.systemui', 'com.android.systemui', 'com.sh.smart.systemui']
    
    def get_keywords(self) -> List[str]:
        """获取SystemUI应用关键词列表"""
        return ['systemui']
