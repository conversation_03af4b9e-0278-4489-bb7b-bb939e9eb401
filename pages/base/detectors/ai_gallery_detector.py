"""
AI Gallery检测器
AI Gallery应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class AiGalleryDetector(BaseAppDetector):
    """AI Gallery应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.AI_GALLERY)
    
    def get_package_names(self) -> List[str]:
        """获取AI Gallery应用包名列表"""
        return ['com.transsion.aigallery', 'com.android.aigallery', 'com.sh.smart.aigallery']
    
    def get_keywords(self) -> List[str]:
        """获取AI Gallery应用关键词列表"""
        return ['aigallery', 'ai gallery']
