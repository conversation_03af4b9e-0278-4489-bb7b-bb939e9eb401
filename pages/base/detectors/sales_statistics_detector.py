"""
SalesStatistics检测器
SalesStatistics应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class SalesStatisticsDetector(BaseAppDetector):
    """SalesStatistics应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.SALES_STATISTICS)
    
    def get_package_names(self) -> List[str]:
        """获取SalesStatistics应用包名列表"""
        return ['com.transsion.salesstatistics', 'com.android.salesstatistics', 'com.sh.smart.salesstatistics']
    
    def get_keywords(self) -> List[str]:
        """获取SalesStatistics应用关键词列表"""
        return ['salesstatistics']
