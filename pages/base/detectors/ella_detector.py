"""
Ella检测器
智能助手Ella应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class EllaDetector(BaseAppDetector):
    """Ella智能助手检测器"""
    
    def __init__(self):
        super().__init__(AppType.ELLA)
    
    def get_package_names(self) -> List[str]:
        """获取Ella应用包名列表"""
        return [
            "com.transsion.aivoiceassistant",
            "com.transsion.ella",
            "com.sh.smart.ella",
            "com.android.ella",
        ]
    
    def get_keywords(self) -> List[str]:
        """获取Ella应用关键词列表"""
        return ["ella", "assistant", "voice", "智能助手", "语音助手"]
