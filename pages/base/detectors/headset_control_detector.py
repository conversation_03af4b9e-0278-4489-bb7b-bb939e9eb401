"""
Headsetcontrol检测器
Headsetcontrol应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class HeadsetControlDetector(BaseAppDetector):
    """Headsetcontrol应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.HEADSET_CONTROL)
    
    def get_package_names(self) -> List[str]:
        """获取Headsetcontrol应用包名列表"""
        return ['com.transsion.headsetcontrol', 'com.android.headsetcontrol', 'com.sh.smart.headsetcontrol']
    
    def get_keywords(self) -> List[str]:
        """获取Headsetcontrol应用关键词列表"""
        return ['headsetcontrol']
