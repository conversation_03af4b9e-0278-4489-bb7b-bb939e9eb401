"""
Clipper检测器
Clipper应用检测器
"""
from typing import List
from pages.base.app_detector import BaseAppDetector, AppType


class ClipperDetector(BaseAppDetector):
    """Clipper应用检测器"""
    
    def __init__(self):
        super().__init__(AppType.CLIPPER)
    
    def get_package_names(self) -> List[str]:
        """获取Clipper应用包名列表"""
        return ['com.transsion.clipper', 'com.android.clipper', 'com.sh.smart.clipper']
    
    def get_keywords(self) -> List[str]:
        """获取Clipper应用关键词列表"""
        return ['clipper']
