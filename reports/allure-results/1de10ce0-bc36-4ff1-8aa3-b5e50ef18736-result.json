{"name": "测试set battery saver settings返回正确的不支持响应", "status": "passed", "description": "验证set battery saver settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: set battery saver settings", "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "status": "passed", "start": 1753765690785, "stop": 1753765702659}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ba688394-ec7b-49af-9aa8-ca4786646785-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "da3f3e9d-d306-43c9-adfc-165200a6cb8e-attachment.png", "type": "image/png"}], "start": 1753765702659, "stop": 1753765703022}], "start": 1753765690785, "stop": 1753765703023}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765703023, "stop": 1753765703029}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "655a48d5-4d7c-4b26-8ab4-32d7ebf04d08-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "1c142a33-a6af-4619-b4a3-c11884b433f8-attachment.png", "type": "image/png"}], "start": 1753765703029, "stop": 1753765703375}], "attachments": [{"name": "stdout", "source": "a30ab0b3-213e-41e5-b881-89c32936d1b9-attachment.txt", "type": "text/plain"}], "start": 1753765690785, "stop": 1753765703376, "uuid": "5df8903d-9d70-4889-9372-009008989ddf", "historyId": "e60dab4e55edacbecf632d4d22f368e6", "testCaseId": "e60dab4e55edacbecf632d4d22f368e6", "fullName": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings#test_set_battery_saver_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_battery_saver_settings"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings"}]}