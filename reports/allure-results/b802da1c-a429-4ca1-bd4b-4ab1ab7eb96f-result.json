{"name": "测试jump to call notifications返回正确的不支持响应", "status": "passed", "description": "验证jump to call notifications指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to call notifications", "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "status": "passed", "start": 1753765390724, "stop": 1753765402925}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ac496551-a0b2-4347-a170-defa52dd4c64-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fc69a141-564a-4345-ad08-07329c8f9fef-attachment.png", "type": "image/png"}], "start": 1753765402925, "stop": 1753765403288}], "start": 1753765390724, "stop": 1753765403289}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765403289, "stop": 1753765403293}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "b55893cd-cb61-4a82-baff-863716fa6c8d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "878d1d19-e9ce-4e30-b49e-b87dddd790bc-attachment.png", "type": "image/png"}], "start": 1753765403293, "stop": 1753765403630}], "attachments": [{"name": "stdout", "source": "cfbc9b99-31b7-4591-b5bb-fd6f066dd98b-attachment.txt", "type": "text/plain"}], "start": 1753765390724, "stop": 1753765403631, "uuid": "a27742f4-8005-480f-a058-e7f30493ff03", "historyId": "540cff5d6d552c22ec37f66efd17315f", "testCaseId": "540cff5d6d552c22ec37f66efd17315f", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications.TestEllaJumpCallNotifications#test_jump_to_call_notifications", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_call_notifications"}, {"name": "subSuite", "value": "TestEllaJumpCallNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications"}]}