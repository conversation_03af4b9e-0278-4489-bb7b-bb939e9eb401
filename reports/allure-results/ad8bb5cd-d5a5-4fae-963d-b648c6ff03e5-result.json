{"name": "测试set floating windows返回正确的不支持响应", "status": "passed", "description": "验证set floating windows指令返回预期的不支持响应", "steps": [{"name": "执行命令: set floating windows", "status": "passed", "steps": [{"name": "执行命令: set floating windows", "status": "passed", "start": 1753765923195, "stop": 1753765931236}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fa6b2bd4-befd-4aaa-9df4-3c5292a0540f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f9680fa4-3a6c-46f3-9cf9-b98d0460746e-attachment.png", "type": "image/png"}], "start": 1753765931236, "stop": 1753765931527}], "start": 1753765923195, "stop": 1753765931529}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765931529, "stop": 1753765931534}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "1b9e2dac-5160-4b74-9661-ea832b4e962a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "92b2731c-76d4-41c0-8cd3-93a65291f344-attachment.png", "type": "image/png"}], "start": 1753765931534, "stop": 1753765931855}], "attachments": [{"name": "stdout", "source": "3c659a1b-7eb8-475b-a622-b68deb026a60-attachment.txt", "type": "text/plain"}], "start": 1753765923195, "stop": 1753765931855, "uuid": "8966b953-ba4e-4458-bae7-7c08a908a8c9", "historyId": "ff945a5d436679bddd13261b231955ec", "testCaseId": "ff945a5d436679bddd13261b231955ec", "fullName": "testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows#test_set_floating_windows", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_floating_windows"}, {"name": "subSuite", "value": "TestEllaSetFloatingWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_floating_windows"}]}