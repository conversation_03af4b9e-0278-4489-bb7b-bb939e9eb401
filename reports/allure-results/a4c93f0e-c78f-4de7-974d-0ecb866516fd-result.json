{"name": "测试how's the weather today?返回正确的不支持响应", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Sorry, weather data error,try later']\nassert False", "trace": "self = <test_how_s_the_weather_today.TestEllaHowSWeatherToday object at 0x0000017E151EB250>, ella_app = <pages.apps.ella.dialogue_page.EllaDialoguePage object at 0x0000017E15488B50>\n\n    @allure.title(f\"测试{command}返回正确的不支持响应\")\n    @allure.description(f\"验证{command}指令返回预期的不支持响应\")\n    @allure.severity(allure.severity_level.NORMAL)\n    @pytest.mark.smoke\n    def test_how_s_the_weather_today(self, ella_app):\n        f\"\"\"{self.command} - 验证不支持指令的响应\"\"\"\n    \n        command = self.command\n    \n        with allure.step(f\"执行命令: {command}\"):\n            initial_status, final_status, response_text = self.simple_command_test(\n                ella_app, command, verify_status=False  # 第三方集成通常不验证状态\n            )\n    \n        with allure.step(\"验证响应包含期望的不支持内容\"):\n            expected_text = self.expected_text\n>           result = self.verify_expected_in_response(expected_text, response_text)\n                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n\ntestcases\\test_ella\\unsupported_commands\\test_how_s_the_weather_today.py:32: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <test_how_s_the_weather_today.TestEllaHowSWeatherToday object at 0x0000017E151EB250>, expected_text = ['Sorry, weather data error,try later']\nresponse_text = [\"how's the weather today?\", 'Minhang District is Mostly Cloudy today. The high is forecast as 32℃ and the low as 26℃.', '', '']\n\n    def verify_expected_in_response(self, expected_text, response_text):\n        \"\"\"\n        验证期望内容是否在响应中\n    \n        Args:\n            expected_text: 期望的文本内容，可以是字符串或字符串列表\n            response_text: 响应文本，可以是字符串或字符串列表\n    \n        Returns:\n            bool: 如果是字符串，返回是否包含；如果是列表，返回是否所有期望都包含\n        \"\"\"\n        log.info(f\"verify_expected_in_response 响应类型: {type(response_text)}, 内容: {response_text}\")\n    \n        # 处理 expected_text 参数\n        if isinstance(expected_text, str):\n            expected_list = [expected_text]\n        elif isinstance(expected_text, list):\n            expected_list = expected_text\n        else:\n            log.error(f\"❌ expected_text类型错误: {type(expected_text)}\")\n            return False\n    \n        # 处理 response_text 参数，统一转换为字符串进行搜索\n        if isinstance(response_text, str):\n            # 如果是字符串，直接使用\n            search_text = response_text\n            log.debug(f\"响应文本(字符串): {search_text}\")\n        elif isinstance(response_text, list):\n            # 如果是列表，过滤空值并合并为一个字符串\n            filtered_texts = [text for text in response_text if text and text.strip()]\n            search_text = \" \".join(filtered_texts)\n            log.debug(f\"响应文本(列表转换): 原始列表={response_text}, 过滤后={filtered_texts}, 合并后={search_text}\")\n        else:\n            log.error(f\"❌ response_text类型错误: {type(response_text)}\")\n            return False\n    \n        # 如果合并后的文本为空，记录警告\n        if not search_text or not search_text.strip():\n            log.warning(\"⚠️ 响应文本为空或只包含空白字符\")\n            search_text = \"\"\n    \n        # 记录所有验证结果\n        all_found = True\n        found_items = []\n        missing_items = []\n    \n        # 遍历所有期望内容\n        for expected_item in expected_list:\n            if not expected_item or not expected_item.strip():\n                log.warning(f\"⚠️ 跳过空的期望内容: '{expected_item}'\")\n                continue\n    \n            # 在合并的文本中搜索\n            if expected_item.lower() in search_text.lower():\n                found_items.append(expected_item)\n                log.info(f\"✅ 响应包含期望内容: '{expected_item}'\")\n            else:\n                missing_items.append(expected_item)\n                log.warning(f\"⚠️ 响应未包含期望内容: '{expected_item}'\")\n                all_found = False\n    \n        # 输出总结\n        if all_found:\n            log.info(f\"🎉 所有期望内容都已找到 ({len(found_items)}/{len(expected_list)})\")\n        else:\n            log.warning(f\"❌ 部分期望内容未找到 ({len(found_items)}/{len(expected_list)})\")\n            log.warning(f\"缺失内容: {missing_items}\")\n            log.warning(f\"搜索文本: '{search_text}'\")\n    \n>       assert all_found, f\"响应未包含期望内容: {missing_items}\"\nE       AssertionError: 响应未包含期望内容: ['Sorry, weather data error,try later']\nE       assert False\n\ntestcases\\test_ella\\base_ella_test.py:1586: AssertionError"}, "description": "验证how's the weather today?指令返回预期的不支持响应", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "steps": [{"name": "执行命令: how's the weather today?", "status": "passed", "start": 1753765192515, "stop": 1753765204748}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5f8f72e6-ac89-4543-9278-182692354f93-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ef1c0e29-223f-4d7b-8df3-012f8055c3c6-attachment.png", "type": "image/png"}], "start": 1753765204748, "stop": 1753765205088}], "start": 1753765192515, "stop": 1753765205090}, {"name": "验证响应包含期望的不支持内容", "status": "failed", "statusDetails": {"message": "AssertionError: 响应未包含期望内容: ['Sorry, weather data error,try later']\nassert False\n", "trace": "  File \"D:\\aigc\\app_test\\testcases\\test_ella\\unsupported_commands\\test_how_s_the_weather_today.py\", line 32, in test_how_s_the_weather_today\n    result = self.verify_expected_in_response(expected_text, response_text)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"D:\\aigc\\app_test\\testcases\\test_ella\\base_ella_test.py\", line 1586, in verify_expected_in_response\n    assert all_found, f\"响应未包含期望内容: {missing_items}\"\n"}, "start": 1753765205090, "stop": 1753765205099}], "attachments": [{"name": "stdout", "source": "07ed9e91-56f2-43ea-ac99-32f780cc3fe2-attachment.txt", "type": "text/plain"}], "start": 1753765192515, "stop": 1753765205101, "uuid": "c68ea1a7-f088-4931-ac80-503c7fb4d418", "historyId": "0ecef7ad2a774fa9a6f37257cfe34f7f", "testCaseId": "0ecef7ad2a774fa9a6f37257cfe34f7f", "fullName": "testcases.test_ella.unsupported_commands.test_how_s_the_weather_today.TestEllaHowSWeatherToday#test_how_s_the_weather_today", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_how_s_the_weather_today"}, {"name": "subSuite", "value": "TestEllaHowSWeatherToday"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_how_s_the_weather_today"}]}