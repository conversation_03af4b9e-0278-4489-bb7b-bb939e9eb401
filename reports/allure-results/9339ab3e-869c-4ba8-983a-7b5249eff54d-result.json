{"name": "测试driving mode返回正确的不支持响应", "status": "passed", "description": "验证driving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: driving mode", "status": "passed", "steps": [{"name": "执行命令: driving mode", "status": "passed", "start": 1753764915936, "stop": 1753764924040}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "d9e67dcd-51b6-44eb-8378-e8972be1224f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "46dbff33-facc-48bf-8768-00111b319740-attachment.png", "type": "image/png"}], "start": 1753764924040, "stop": 1753764924360}], "start": 1753764915936, "stop": 1753764924361}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764924361, "stop": 1753764924365}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "26de99ab-ad80-447c-a14d-af68d4973b05-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e5c11721-6e2d-4a16-a570-d5c07b675256-attachment.png", "type": "image/png"}], "start": 1753764924365, "stop": 1753764924700}], "attachments": [{"name": "stdout", "source": "16713bb9-e2d3-40ba-a63f-25f087dabd44-attachment.txt", "type": "text/plain"}], "start": 1753764915935, "stop": 1753764924701, "uuid": "a5b60373-16ac-4dae-88d2-3ca049a258ee", "historyId": "3215de286c6ddd59d6e52a44f2a9967d", "testCaseId": "3215de286c6ddd59d6e52a44f2a9967d", "fullName": "testcases.test_ella.unsupported_commands.test_driving_mode.TestEllaDrivingMode#test_driving_mode", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_driving_mode"}, {"name": "subSuite", "value": "TestEllaDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_driving_mode"}]}