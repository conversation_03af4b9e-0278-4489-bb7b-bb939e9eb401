{"name": "测试turn on high brightness mode返回正确的不支持响应", "status": "passed", "description": "验证turn on high brightness mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: turn on high brightness mode", "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "status": "passed", "start": 1753766654111, "stop": 1753766662278}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "34642051-a02e-4b18-b904-42a4c754afca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "f11d0318-a1fe-4660-8b08-00ff1f9a5a80-attachment.png", "type": "image/png"}], "start": 1753766662278, "stop": 1753766662576}], "start": 1753766654111, "stop": 1753766662577}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766662577, "stop": 1753766662582}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e1e9b819-a790-4082-bec5-24cb0409b946-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2930f915-6a67-4c00-a33b-7dce695da408-attachment.png", "type": "image/png"}], "start": 1753766662582, "stop": 1753766662934}], "attachments": [{"name": "stdout", "source": "8d793af1-40e5-4c11-91c5-686a0d03f04d-attachment.txt", "type": "text/plain"}], "start": 1753766654111, "stop": 1753766662935, "uuid": "da7f9e29-6dce-4929-a563-750866100a16", "historyId": "599b7a465f619c38a4638073f59c38c0", "testCaseId": "599b7a465f619c38a4638073f59c38c0", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode#test_turn_on_high_brightness_mode", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_high_brightness_mode"}, {"name": "subSuite", "value": "TestEllaTurnHighBrightnessMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode"}]}