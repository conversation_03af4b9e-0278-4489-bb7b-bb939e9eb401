{"name": "测试order a takeaway返回正确的不支持响应", "status": "passed", "description": "验证order a takeaway指令返回预期的不支持响应", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "steps": [{"name": "执行命令: order a takeaway", "status": "passed", "start": 1753765597096, "stop": 1753765604836}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "830f0ccf-a0ce-4a3d-841e-d8d9407fd26b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "edc15039-9ed6-465b-873b-c6f12548f904-attachment.png", "type": "image/png"}], "start": 1753765604836, "stop": 1753765605109}], "start": 1753765597096, "stop": 1753765605112}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765605112, "stop": 1753765605115}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ac9e31c5-1580-4ed7-976e-b53c07343495-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "5518e224-c99a-489a-87b9-4ca1a76b7aa9-attachment.png", "type": "image/png"}], "start": 1753765605115, "stop": 1753765605450}], "attachments": [{"name": "stdout", "source": "dd1958dc-f941-4814-af04-882fbd7d99e0-attachment.txt", "type": "text/plain"}], "start": 1753765597096, "stop": 1753765605451, "uuid": "a2a14811-2df7-4513-b313-a2ce50b2f239", "historyId": "43a8e6496d8d78f2b8bc066858f8bdd9", "testCaseId": "43a8e6496d8d78f2b8bc066858f8bdd9", "fullName": "testcases.test_ella.unsupported_commands.test_order_a_takeaway.TestEllaOrderTakeaway#test_order_a_takeaway", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_order_a_takeaway"}, {"name": "subSuite", "value": "TestEllaOrderTakeaway"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_order_a_takeaway"}]}