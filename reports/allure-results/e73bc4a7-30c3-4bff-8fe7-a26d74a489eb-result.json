{"name": "测试enable touch optimization返回正确的不支持响应", "status": "passed", "description": "验证enable touch optimization指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable touch optimization", "status": "passed", "steps": [{"name": "执行命令: enable touch optimization", "status": "passed", "start": 1753765125128, "stop": 1753765133200}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6a94bd78-9259-4e38-b9bb-c9c2ee488794-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "79898a03-89b5-414a-b014-48450204d290-attachment.png", "type": "image/png"}], "start": 1753765133200, "stop": 1753765133507}], "start": 1753765125128, "stop": 1753765133508}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765133508, "stop": 1753765133512}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "27ad673c-7aa1-41d7-bf44-b5f501689c53-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8fe0bfd-41c1-47ef-826a-385e249f28c6-attachment.png", "type": "image/png"}], "start": 1753765133512, "stop": 1753765133835}], "attachments": [{"name": "stdout", "source": "4497b30d-9dec-44e9-bf60-fff8d10f5bb1-attachment.txt", "type": "text/plain"}], "start": 1753765125128, "stop": 1753765133836, "uuid": "80161639-b6ee-4799-90d1-5e911b47c076", "historyId": "fc75b92fb4a100575b2c948dd6c5a008", "testCaseId": "fc75b92fb4a100575b2c948dd6c5a008", "fullName": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization.TestEllaEnableTouchOptimization#test_enable_touch_optimization", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaEnableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_touch_optimization"}]}