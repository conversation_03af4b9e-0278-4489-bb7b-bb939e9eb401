{"name": "测试set my fonts返回正确的不支持响应", "status": "passed", "description": "验证set my fonts指令返回预期的不支持响应", "steps": [{"name": "执行命令: set my fonts", "status": "passed", "steps": [{"name": "执行命令: set my fonts", "status": "passed", "start": 1753766062593, "stop": 1753766070760}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "42c44356-b396-49c8-8405-ce79a5292b6b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "214c1f40-4c96-40e9-9d83-ef66d28e0f6e-attachment.png", "type": "image/png"}], "start": 1753766070761, "stop": 1753766071091}], "start": 1753766062593, "stop": 1753766071092}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766071092, "stop": 1753766071097}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9a0785c7-ff54-45e9-9583-e914958ef38d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "fa339fc1-759e-4af1-a2b0-82ae796d8c6e-attachment.png", "type": "image/png"}], "start": 1753766071097, "stop": 1753766071389}], "attachments": [{"name": "stdout", "source": "fa1e8f71-8fce-4bbd-afdb-8b032a294a06-attachment.txt", "type": "text/plain"}], "start": 1753766062593, "stop": 1753766071390, "uuid": "31a15bc8-5588-41d8-8cf4-df41f16cbdfa", "historyId": "7d3b4e67344145885187c529ee88a9aa", "testCaseId": "7d3b4e67344145885187c529ee88a9aa", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts#test_set_my_fonts", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_fonts"}, {"name": "subSuite", "value": "TestEllaSetMyFonts"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_fonts"}]}