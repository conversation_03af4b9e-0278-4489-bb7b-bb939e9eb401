{"name": "测试Enable Network Enhancement返回正确的不支持响应", "status": "passed", "description": "验证Enable Network Enhancement指令返回预期的不支持响应", "steps": [{"name": "执行命令: Enable Network Enhancement", "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "status": "passed", "start": 1753765080707, "stop": 1753765088428}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "3732586b-abc6-4625-90b5-2cd64aac1e50-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "359ba722-d386-4924-83ff-7e55fd07667a-attachment.png", "type": "image/png"}], "start": 1753765088428, "stop": 1753765088664}], "start": 1753765080707, "stop": 1753765088665}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765088665, "stop": 1753765088670}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "ebc8e9d3-3761-467c-a0a2-d1ce775631ea-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "80f5507a-82d5-48e8-b930-2bcd6400165b-attachment.png", "type": "image/png"}], "start": 1753765088670, "stop": 1753765088964}], "attachments": [{"name": "stdout", "source": "cff57aac-127d-4b75-9281-c71252417e28-attachment.txt", "type": "text/plain"}], "start": 1753765080707, "stop": 1753765088965, "uuid": "3a1c12f8-5f44-458a-8b4f-81fb6ffd7b16", "historyId": "657acdf17dda1a11abf6946763f6ed52", "testCaseId": "657acdf17dda1a11abf6946763f6ed52", "fullName": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement.TestEllaEnableNetworkEnhancement#test_enable_network_enhancement", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaEnableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement"}]}