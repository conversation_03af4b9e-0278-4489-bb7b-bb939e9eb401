{"name": "测试enable all ai magic box features返回正确的不支持响应", "status": "passed", "description": "验证enable all ai magic box features指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable all ai magic box features", "status": "passed", "steps": [{"name": "执行命令: enable all ai magic box features", "status": "passed", "start": 1753764959912, "stop": 1753764968167}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6c94098b-68fb-40bb-80e6-9df0d2b802a2-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "d31ac4b7-435a-4aaf-9c47-6d70e0a36d9f-attachment.png", "type": "image/png"}], "start": 1753764968167, "stop": 1753764968515}], "start": 1753764959912, "stop": 1753764968516}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764968516, "stop": 1753764968521}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7f61a94f-4b39-4947-81fc-ba8137e16c51-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4d30d68e-4fc7-4d05-8997-282b74b22ec2-attachment.png", "type": "image/png"}], "start": 1753764968521, "stop": 1753764968912}], "attachments": [{"name": "stdout", "source": "5cd1b613-e6f1-4337-b74f-3b2da1b19f4d-attachment.txt", "type": "text/plain"}], "start": 1753764959912, "stop": 1753764968912, "uuid": "8b9f4b25-9150-4c15-96f2-8ab6ce24b5ed", "historyId": "d4ded95517fa8a5af49f09554cc49725", "testCaseId": "d4ded95517fa8a5af49f09554cc49725", "fullName": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features.TestEllaEnableAllAiMagicBoxFeatures#test_enable_all_ai_magic_box_features", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaEnableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_all_ai_magic_box_features"}]}