{"name": "测试set personal hotspot返回正确的不支持响应", "status": "passed", "description": "验证set personal hotspot指令返回预期的不支持响应", "steps": [{"name": "执行命令: set personal hotspot", "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "status": "passed", "start": 1753766129315, "stop": 1753766137742}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dcdc3cae-f0d0-4140-9b35-43850136d9cb-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a216aa9e-19e6-4e47-bf58-a63cd66630c3-attachment.png", "type": "image/png"}], "start": 1753766137743, "stop": 1753766138091}], "start": 1753766129315, "stop": 1753766138092}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766138092, "stop": 1753766138096}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "24d7f8d3-a271-4528-88aa-26a8d9608e92-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "c064527f-d4df-491f-b632-fe48aaa2f47b-attachment.png", "type": "image/png"}], "start": 1753766138096, "stop": 1753766138465}], "attachments": [{"name": "stdout", "source": "7402a699-ce99-4043-803b-06545d650d7a-attachment.txt", "type": "text/plain"}], "start": 1753766129315, "stop": 1753766138465, "uuid": "cf5df340-fd44-4b7f-b840-88aec73c524b", "historyId": "4f538fc772535a0c0811ad87d3aa9494", "testCaseId": "4f538fc772535a0c0811ad87d3aa9494", "fullName": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot.TestEllaSetPersonalHotspot#test_set_personal_hotspot", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_personal_hotspot"}, {"name": "subSuite", "value": "TestEllaSetPersonalHotspot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot"}]}