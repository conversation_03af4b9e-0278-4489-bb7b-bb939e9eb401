{"name": "测试enable auto pickup返回正确的不支持响应", "status": "passed", "description": "验证enable auto pickup指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable auto pickup", "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "status": "passed", "start": 1753764982371, "stop": 1753764990673}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "824ea3b8-78b9-4359-a7ea-3c9bf5da0a72-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4c26d5e6-bbf3-4387-879f-ba721693a06c-attachment.png", "type": "image/png"}], "start": 1753764990673, "stop": 1753764990969}], "start": 1753764982371, "stop": 1753764990970}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764990970, "stop": 1753764990975}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4beeb0d1-36ff-4fc3-a22a-5f0d0445fe62-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "177f68a0-cddc-4d15-8e35-b0dcf243ae99-attachment.png", "type": "image/png"}], "start": 1753764990975, "stop": 1753764991327}], "attachments": [{"name": "stdout", "source": "25409c5e-ce1c-4b85-b7cb-aef06fafbfb2-attachment.txt", "type": "text/plain"}], "start": 1753764982371, "stop": 1753764991328, "uuid": "51573f4d-8a2b-45d2-bf33-19d3a4094835", "historyId": "57acf2797af332487c1fdb9a53a30e4f", "testCaseId": "57acf2797af332487c1fdb9a53a30e4f", "fullName": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup.TestEllaEnableAutoPickup#test_enable_auto_pickup", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaEnableAutoPickup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup"}]}