{"name": "测试disable unfreeze返回正确的不支持响应", "status": "passed", "description": "验证disable unfreeze指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable unfreeze", "status": "passed", "steps": [{"name": "执行命令: disable unfreeze", "status": "passed", "start": 1753764845156, "stop": 1753764853368}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "9151332f-9406-4604-a224-ef49dbc6a980-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "2fc2ade5-46ed-42f6-96a9-1fd96f6266bb-attachment.png", "type": "image/png"}], "start": 1753764853368, "stop": 1753764853757}], "start": 1753764845156, "stop": 1753764853759}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764853759, "stop": 1753764853763}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "697b3fb6-d3f8-4ab0-aa54-51ea03e4e2d8-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "41d36ab7-09b1-4726-91e7-e4e76bb3f12f-attachment.png", "type": "image/png"}], "start": 1753764853763, "stop": 1753764854156}], "attachments": [{"name": "stdout", "source": "a0fc10fe-f049-46f6-8fae-1f8fcc6f0138-attachment.txt", "type": "text/plain"}], "start": 1753764845156, "stop": 1753764854156, "uuid": "0f8eec12-00e5-4f00-98aa-061dc0e51989", "historyId": "1695232002b2ad29ffa1faf52965470d", "testCaseId": "1695232002b2ad29ffa1faf52965470d", "fullName": "testcases.test_ella.unsupported_commands.test_disable_unfreeze.TestEllaDisableUnfreeze#test_disable_unfreeze", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_unfreeze"}, {"name": "subSuite", "value": "TestEllaDisableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_unfreeze"}]}