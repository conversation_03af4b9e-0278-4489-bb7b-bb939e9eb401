{"name": "测试set split-screen apps返回正确的不支持响应", "status": "passed", "description": "验证set split-screen apps指令返回预期的不支持响应", "steps": [{"name": "执行命令: set split-screen apps", "status": "passed", "steps": [{"name": "执行命令: set split-screen apps", "status": "passed", "start": 1753766405436, "stop": 1753766414423}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "2cb0fefc-3d9c-41e7-9315-7255516f5233-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "eeba821b-22ef-4894-881b-de26174dd59e-attachment.png", "type": "image/png"}], "start": 1753766414423, "stop": 1753766414747}], "start": 1753766405436, "stop": 1753766414749}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766414749, "stop": 1753766414753}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fcb57efb-b325-4f1f-b7ef-c97fcd4e0b32-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "81cce8d6-9d1e-432c-9b38-6a75796190e2-attachment.png", "type": "image/png"}], "start": 1753766414753, "stop": 1753766415095}], "attachments": [{"name": "stdout", "source": "5e15b3a0-7ad5-4724-9607-a81c2a417949-attachment.txt", "type": "text/plain"}], "start": 1753766405436, "stop": 1753766415096, "uuid": "4659f370-b782-4c15-9032-c1012b8c9794", "historyId": "b5e1711cce3102fc710ff74e18bf9129", "testCaseId": "b5e1711cce3102fc710ff74e18bf9129", "fullName": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps.TestEllaSetSplitScreenApps#test_set_split_screen_apps", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_split_screen_apps"}, {"name": "subSuite", "value": "TestEllaSetSplitScreenApps"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_split_screen_apps"}]}