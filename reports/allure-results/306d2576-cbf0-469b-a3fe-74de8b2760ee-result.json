{"name": "测试enable accelerate dialogue返回正确的不支持响应", "status": "passed", "description": "验证enable accelerate dialogue指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable accelerate dialogue", "status": "passed", "steps": [{"name": "执行命令: enable accelerate dialogue", "status": "passed", "start": 1753764937838, "stop": 1753764946131}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "6ccc76ba-d5f8-408f-beb1-4f7432b8813d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "aad8019a-862a-4987-a5af-2ed3714ced61-attachment.png", "type": "image/png"}], "start": 1753764946131, "stop": 1753764946461}], "start": 1753764937838, "stop": 1753764946462}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764946462, "stop": 1753764946468}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "e6cd8ab8-1d44-42e5-bea2-11fbb5b24dc1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e75c26cf-de44-4408-a212-5b5e9ac92140-attachment.png", "type": "image/png"}], "start": 1753764946468, "stop": 1753764946849}], "attachments": [{"name": "stdout", "source": "d32fd323-633a-45f6-aa15-91e327a107f5-attachment.txt", "type": "text/plain"}], "start": 1753764937838, "stop": 1753764946850, "uuid": "7fb46957-98d1-4e3c-ae66-ba599e0b21f9", "historyId": "0e5513d569c7270e4332e484218ae36b", "testCaseId": "0e5513d569c7270e4332e484218ae36b", "fullName": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue.TestEllaEnableAccelerateDialogue#test_enable_accelerate_dialogue", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaEnableAccelerateDialogue"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_accelerate_dialogue"}]}