{"name": "测试switch to power saving mode返回正确的不支持响应", "status": "passed", "description": "验证switch to power saving mode指令返回预期的不支持响应", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "status": "passed", "start": 1753766519465, "stop": 1753766527640}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "87a9a461-06e5-4c6f-a1e6-6d32fbefdd34-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "ed16e9c2-54cf-48d0-a36f-bc0f2a7d7a9d-attachment.png", "type": "image/png"}], "start": 1753766527640, "stop": 1753766527896}], "start": 1753766519465, "stop": 1753766527897}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766527897, "stop": 1753766527901}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "4782d1c7-46d8-4282-aec0-e895da4698ca-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b8e28aa9-39ae-4609-b09b-eb5f19e88431-attachment.png", "type": "image/png"}], "start": 1753766527901, "stop": 1753766528221}], "attachments": [{"name": "stdout", "source": "d12c96af-8f2e-43da-a6ff-eb58fd5ec43e-attachment.txt", "type": "text/plain"}], "start": 1753766519465, "stop": 1753766528222, "uuid": "42e71166-1c0d-48bc-9203-51fdcd5ece08", "historyId": "a0efcebc4cee6024e690bd290b4f3fbb", "testCaseId": "a0efcebc4cee6024e690bd290b4f3fbb", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode.TestEllaSwitchPowerSavingMode#test_switch_to_power_saving_mode", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode"}]}