{"name": "测试set screen refresh rate返回正确的不支持响应", "status": "passed", "description": "验证set screen refresh rate指令返回预期的不支持响应", "steps": [{"name": "执行命令: set screen refresh rate", "status": "passed", "steps": [{"name": "执行命令: set screen refresh rate", "status": "passed", "start": 1753766224733, "stop": 1753766232390}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a012225a-1229-4e2b-b7c8-fc0d230ccd2f-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6681bf7b-dfbb-422e-b022-570ec0206223-attachment.png", "type": "image/png"}], "start": 1753766232390, "stop": 1753766232739}], "start": 1753766224733, "stop": 1753766232740}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766232740, "stop": 1753766232745}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "30c2831f-ac2e-45fc-b9a6-efcfa28c7e4a-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "68f86139-978b-4035-83fb-d2216926216a-attachment.png", "type": "image/png"}], "start": 1753766232745, "stop": 1753766233135}], "attachments": [{"name": "stdout", "source": "fb4a59c9-b66b-40f0-abd2-b8dd91eaff0a-attachment.txt", "type": "text/plain"}], "start": 1753766224733, "stop": 1753766233136, "uuid": "67d245fc-ce38-42dd-a50b-aae2190e1ebb", "historyId": "1bc9389e45f0f75c30d3dfb39134948d", "testCaseId": "1bc9389e45f0f75c30d3dfb39134948d", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate.TestEllaSetScreenRefreshRate#test_set_screen_refresh_rate", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_refresh_rate"}, {"name": "subSuite", "value": "TestEllaSetScreenRefreshRate"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_refresh_rate"}]}