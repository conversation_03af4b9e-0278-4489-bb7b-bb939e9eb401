{"name": "测试disable touch optimization返回正确的不支持响应", "status": "passed", "description": "验证disable touch optimization指令返回预期的不支持响应", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "steps": [{"name": "执行命令: disable touch optimization", "status": "passed", "start": 1753764823176, "stop": 1753764831083}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "8afddc59-4677-4743-af28-3fdbda5b546b-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cdf31683-d99d-4b35-9c9b-54dc370cc0d9-attachment.png", "type": "image/png"}], "start": 1753764831083, "stop": 1753764831396}], "start": 1753764823176, "stop": 1753764831396}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753764831396, "stop": 1753764831400}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "48f24905-738d-45a6-a3d0-1d653170b8d1-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "0f392e0a-083f-435a-a978-9daa7518e2b6-attachment.png", "type": "image/png"}], "start": 1753764831400, "stop": 1753764831727}], "attachments": [{"name": "stdout", "source": "f8009216-5ff8-4e06-aae9-1fc0268ea0ba-attachment.txt", "type": "text/plain"}], "start": 1753764823176, "stop": 1753764831729, "uuid": "2b13239d-2346-4e6d-b683-e167af26ce20", "historyId": "0b659537bc9c9b47c2c23f702fadd56b", "testCaseId": "0b659537bc9c9b47c2c23f702fadd56b", "fullName": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization.TestEllaDisableTouchOptimization#test_disable_touch_optimization", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_touch_optimization"}, {"name": "subSuite", "value": "TestEllaDisableTouchOptimization"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_touch_optimization"}]}