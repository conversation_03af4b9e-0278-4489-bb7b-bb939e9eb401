{"name": "测试enable unfreeze返回正确的不支持响应", "status": "passed", "description": "验证enable unfreeze指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable unfreeze", "status": "passed", "steps": [{"name": "执行命令: enable unfreeze", "status": "passed", "start": 1753765147303, "stop": 1753765155088}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "a128d932-0280-423c-9afa-beece0d067e0-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "93a754ab-b91a-4bc3-894f-11a9e57faff6-attachment.png", "type": "image/png"}], "start": 1753765155088, "stop": 1753765155390}], "start": 1753765147303, "stop": 1753765155392}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765155392, "stop": 1753765155397}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "53d60e30-b4cb-4ccc-9d49-f78a38d091aa-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "cbe250f6-b0bc-4539-b330-56db4dec48e9-attachment.png", "type": "image/png"}], "start": 1753765155397, "stop": 1753765155708}], "attachments": [{"name": "stdout", "source": "ad90e6b2-a54b-4da9-a03e-5389d18c1784-attachment.txt", "type": "text/plain"}], "start": 1753765147303, "stop": 1753765155709, "uuid": "feb0d31e-f65f-402b-80a3-b5a9a282d80f", "historyId": "afa6af304cfb25a990764680de5fa777", "testCaseId": "afa6af304cfb25a990764680de5fa777", "fullName": "testcases.test_ella.unsupported_commands.test_enable_unfreeze.TestEllaEnableUnfreeze#test_enable_unfreeze", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_unfreeze"}, {"name": "subSuite", "value": "TestEllaEnableUnfreeze"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_unfreeze"}]}