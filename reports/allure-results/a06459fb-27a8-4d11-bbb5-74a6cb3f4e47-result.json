{"name": "测试enable zonetouch master返回正确的不支持响应", "status": "passed", "description": "验证enable zonetouch master指令返回预期的不支持响应", "steps": [{"name": "执行命令: enable zonetouch master", "status": "passed", "steps": [{"name": "执行命令: enable zonetouch master", "status": "passed", "start": 1753765170109, "stop": 1753765178318}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "7b002392-0cd9-401a-9312-aa920b20c072-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "6fe9f9b4-15a4-4a09-bfef-4606a5e29a56-attachment.png", "type": "image/png"}], "start": 1753765178318, "stop": 1753765178586}], "start": 1753765170109, "stop": 1753765178588}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765178588, "stop": 1753765178591}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "5ff37c5f-3ea7-4fbc-9170-44a377a29160-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "87162313-abee-44ef-8365-4a3f2a50e5d1-attachment.png", "type": "image/png"}], "start": 1753765178591, "stop": 1753765178908}], "attachments": [{"name": "stdout", "source": "a9b8303e-adf1-43b0-8d7a-1eb6d782da42-attachment.txt", "type": "text/plain"}], "start": 1753765170109, "stop": 1753765178909, "uuid": "d6cb8c52-2c00-48e7-8142-4f978b6740ae", "historyId": "7a670647c2336e6a5a5d07824fe89da6", "testCaseId": "7a670647c2336e6a5a5d07824fe89da6", "fullName": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master.TestEllaEnableZonetouchMaster#test_enable_zonetouch_master", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_zonetouch_master"}, {"name": "subSuite", "value": "TestEllaEnableZonetouchMaster"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_zonetouch_master"}]}