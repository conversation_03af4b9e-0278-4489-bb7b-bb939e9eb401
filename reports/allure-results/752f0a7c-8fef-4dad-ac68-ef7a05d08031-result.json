{"name": "测试set my themes返回正确的不支持响应", "status": "passed", "description": "验证set my themes指令返回预期的不支持响应", "steps": [{"name": "执行命令: set my themes", "status": "passed", "steps": [{"name": "执行命令: set my themes", "status": "passed", "start": 1753766085120, "stop": 1753766092997}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fab42c0f-da43-4531-b48c-ebb5d184cb51-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "4c193fe7-244b-47e8-a41c-540a83436deb-attachment.png", "type": "image/png"}], "start": 1753766092997, "stop": 1753766093279}], "start": 1753766085120, "stop": 1753766093280}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753766093280, "stop": 1753766093285}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "bfa24260-86bd-4501-8476-bb0518f976c6-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "b59a2f41-a195-428e-8975-66ec324be41b-attachment.png", "type": "image/png"}], "start": 1753766093285, "stop": 1753766093577}], "attachments": [{"name": "stdout", "source": "ce691225-d92f-4356-a000-fc835030b925-attachment.txt", "type": "text/plain"}], "start": 1753766085120, "stop": 1753766093577, "uuid": "bd76a400-1884-4e1b-bb61-3873dafc345d", "historyId": "084048a337d3081654dc4414f67fce70", "testCaseId": "084048a337d3081654dc4414f67fce70", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_themes.TestEllaSetMyThemes#test_set_my_themes", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_themes"}, {"name": "subSuite", "value": "TestEllaSetMyThemes"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_themes"}]}