{"name": "测试jump to notifications and status bar settings返回正确的不支持响应", "status": "passed", "description": "验证jump to notifications and status bar settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "status": "passed", "steps": [{"name": "执行命令: jump to notifications and status bar settings", "status": "passed", "start": 1753765470565, "stop": 1753765482287}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "387437f3-b9dc-46ff-9479-38f1dab722f9-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "a99f2687-e8a5-4ba9-9467-79b9c7291dee-attachment.png", "type": "image/png"}], "start": 1753765482287, "stop": 1753765482618}], "start": 1753765470565, "stop": 1753765482621}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765482621, "stop": 1753765482624}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "dddd1763-98ce-401a-8e01-0ef4e25a01da-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "92d1aef8-a588-4286-9a46-ce2b7c94e3fb-attachment.png", "type": "image/png"}], "start": 1753765482624, "stop": 1753765483037}], "attachments": [{"name": "stdout", "source": "f37ea529-f06c-4d44-a53e-afa2673787e1-attachment.txt", "type": "text/plain"}], "start": 1753765470565, "stop": 1753765483040, "uuid": "8e6b40dd-1a96-4240-9a55-b781207dbb1b", "historyId": "d8bd499fa9e4e04741c5c255fac9036d", "testCaseId": "d8bd499fa9e4e04741c5c255fac9036d", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings.TestEllaJumpNotificationsStatusBarSettings#test_jump_to_notifications_and_status_bar_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_notifications_and_status_bar_settings"}, {"name": "subSuite", "value": "TestEllaJumpNotificationsStatusBarSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_notifications_and_status_bar_settings"}]}