{"name": "测试open font family settings返回正确的不支持响应", "status": "passed", "description": "验证open font family settings指令返回预期的不支持响应", "steps": [{"name": "执行命令: open font family settings", "status": "passed", "steps": [{"name": "执行命令: open font family settings", "status": "passed", "start": 1753765522695, "stop": 1753765534190}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "fffa4fa2-2326-47bd-9ead-c40038efee9d-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "e5727fc7-6bc1-4724-a90d-e7491508c1ee-attachment.png", "type": "image/png"}], "start": 1753765534190, "stop": 1753765534477}], "start": 1753765522695, "stop": 1753765534478}, {"name": "验证响应包含期望的不支持内容", "status": "passed", "start": 1753765534478, "stop": 1753765534484}, {"name": "记录测试结果", "status": "passed", "attachments": [{"name": "测试总结", "source": "213196aa-67f4-4847-8b7a-f7e9c1929542-attachment.txt", "type": "text/plain"}, {"name": "test_completed", "source": "8f407daa-d086-4f3a-ab7e-fd6fc3df34d8-attachment.png", "type": "image/png"}], "start": 1753765534484, "stop": 1753765534822}], "attachments": [{"name": "stdout", "source": "a3f68fe3-173e-475f-a8d5-4025b3bbe795-attachment.txt", "type": "text/plain"}], "start": 1753765522695, "stop": 1753765534823, "uuid": "e5e9ecb3-5981-472e-9b64-78a878c395c8", "historyId": "fd79b0d35f1f4639521f70b269d3aadc", "testCaseId": "fd79b0d35f1f4639521f70b269d3aadc", "fullName": "testcases.test_ella.unsupported_commands.test_open_font_family_settings.TestEllaOpenSettings#test_open_font_family_settings", "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_open_font_family_settings"}, {"name": "subSuite", "value": "TestEllaOpenSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_open_font_family_settings"}]}