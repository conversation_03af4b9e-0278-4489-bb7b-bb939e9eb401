{"uid": "9b68c7b52f218f6f", "name": "测试Enable Network Enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement.TestEllaEnableNetworkEnhancement#test_enable_network_enhancement", "historyId": "657acdf17dda1a11abf6946763f6ed52", "time": {"start": 1753765080707, "stop": 1753765088965, "duration": 8258}, "description": "验证Enable Network Enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Network Enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765067901, "stop": 1753765080706, "duration": 12805}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765080706, "stop": 1753765080706, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证Enable Network Enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1753765080707, "stop": 1753765088665, "duration": 7958}, "status": "passed", "steps": [{"name": "执行命令: Enable Network Enhancement", "time": {"start": 1753765080707, "stop": 1753765088428, "duration": 7721}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765088428, "stop": 1753765088664, "duration": 236}, "status": "passed", "steps": [], "attachments": [{"uid": "86b290d4b1d19cf9", "name": "测试总结", "source": "86b290d4b1d19cf9.txt", "type": "text/plain", "size": 246}, {"uid": "ae021e4305de9a07", "name": "test_completed", "source": "ae021e4305de9a07.png", "type": "image/png", "size": 645209}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765088665, "stop": 1753765088670, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765088670, "stop": 1753765088964, "duration": 294}, "status": "passed", "steps": [], "attachments": [{"uid": "4759500e719be13b", "name": "测试总结", "source": "4759500e719be13b.txt", "type": "text/plain", "size": 246}, {"uid": "abee571c3cfb8dd6", "name": "test_completed", "source": "abee571c3cfb8dd6.png", "type": "image/png", "size": 645128}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1d0e8a5c9ab5c8d4", "name": "stdout", "source": "1d0e8a5c9ab5c8d4.txt", "type": "text/plain", "size": 10604}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765088967, "stop": 1753765088967, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765088969, "stop": 1753765090285, "duration": 1316}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaEnableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "9b68c7b52f218f6f.json", "parameterValues": []}