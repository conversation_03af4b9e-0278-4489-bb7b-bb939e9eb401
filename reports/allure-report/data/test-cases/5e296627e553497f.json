{"uid": "5e296627e553497f", "name": "测试turn off driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode.TestEllaTurnOffDrivingMode#test_turn_off_driving_mode", "historyId": "984a0fd313bba0ca2f20f0bbff732eb8", "time": {"start": 1753766586941, "stop": 1753766595841, "duration": 8900}, "description": "验证turn off driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn off driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766574094, "stop": 1753766586940, "duration": 12846}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766586940, "stop": 1753766586940, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证turn off driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1753766586941, "stop": 1753766595508, "duration": 8567}, "status": "passed", "steps": [{"name": "执行命令: turn off driving mode", "time": {"start": 1753766586941, "stop": 1753766595188, "duration": 8247}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766595188, "stop": 1753766595507, "duration": 319}, "status": "passed", "steps": [], "attachments": [{"uid": "2f005d6d6734a84c", "name": "测试总结", "source": "2f005d6d6734a84c.txt", "type": "text/plain", "size": 232}, {"uid": "ee6903bd9ea81883", "name": "test_completed", "source": "ee6903bd9ea81883.png", "type": "image/png", "size": 643394}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766595508, "stop": 1753766595512, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766595512, "stop": 1753766595839, "duration": 327}, "status": "passed", "steps": [], "attachments": [{"uid": "10122c98fd22a7f1", "name": "测试总结", "source": "10122c98fd22a7f1.txt", "type": "text/plain", "size": 232}, {"uid": "54fe96138fbe56b3", "name": "test_completed", "source": "54fe96138fbe56b3.png", "type": "image/png", "size": 643055}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "e60f3b25d44a2d19", "name": "stdout", "source": "e60f3b25d44a2d19.txt", "type": "text/plain", "size": 10526}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766595843, "stop": 1753766595843, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766595845, "stop": 1753766597128, "duration": 1283}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_off_driving_mode"}, {"name": "subSuite", "value": "TestEllaTurnOffDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_off_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "5e296627e553497f.json", "parameterValues": []}