{"uid": "f72ab1da7fdcbd71", "name": "测试set phone number返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_phone_number.TestEllaSetPhoneNumber#test_set_phone_number", "historyId": "8e367b8da758818b9a0fe21deca7ec48", "time": {"start": 1753766175014, "stop": 1753766188180, "duration": 13166}, "description": "验证set phone number指令返回预期的不支持响应", "descriptionHtml": "<p>验证set phone number指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766162427, "stop": 1753766175013, "duration": 12586}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766175013, "stop": 1753766175013, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set phone number指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1753766175014, "stop": 1753766187843, "duration": 12829}, "status": "passed", "steps": [{"name": "执行命令: set phone number", "time": {"start": 1753766175014, "stop": 1753766187540, "duration": 12526}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766187540, "stop": 1753766187842, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "ce51c7b92071a2ef", "name": "测试总结", "source": "ce51c7b92071a2ef.txt", "type": "text/plain", "size": 225}, {"uid": "8c9e748361f4812", "name": "test_completed", "source": "8c9e748361f4812.png", "type": "image/png", "size": 644913}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766187843, "stop": 1753766187846, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766187846, "stop": 1753766188179, "duration": 333}, "status": "passed", "steps": [], "attachments": [{"uid": "6d54adeb1f843191", "name": "测试总结", "source": "6d54adeb1f843191.txt", "type": "text/plain", "size": 225}, {"uid": "6b553f3c1805212b", "name": "test_completed", "source": "6b553f3c1805212b.png", "type": "image/png", "size": 645418}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "7a0b6a0fd9be7bf1", "name": "stdout", "source": "7a0b6a0fd9be7bf1.txt", "type": "text/plain", "size": 11431}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766188182, "stop": 1753766188182, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766188183, "stop": 1753766189442, "duration": 1259}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_phone_number"}, {"name": "subSuite", "value": "TestEllaSetPhoneNumber"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_phone_number"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f72ab1da7fdcbd71.json", "parameterValues": []}