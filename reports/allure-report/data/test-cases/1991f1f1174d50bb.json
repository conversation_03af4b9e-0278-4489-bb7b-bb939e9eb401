{"uid": "1991f1f1174d50bb", "name": "测试yandex eats返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_yandex_eats.TestEllaYandexEats#test_yandex_eats", "historyId": "b911308f3c1fe764715d778a884946c2", "time": {"start": 1753766727520, "stop": 1753766736122, "duration": 8602}, "description": "验证yandex eats指令返回预期的不支持响应", "descriptionHtml": "<p>验证yandex eats指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766714271, "stop": 1753766727520, "duration": 13249}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766727520, "stop": 1753766727520, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证yandex eats指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1753766727521, "stop": 1753766735778, "duration": 8257}, "status": "passed", "steps": [{"name": "执行命令: yandex eats", "time": {"start": 1753766727521, "stop": 1753766735478, "duration": 7957}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766735478, "stop": 1753766735777, "duration": 299}, "status": "passed", "steps": [], "attachments": [{"uid": "e872f77b4742d477", "name": "测试总结", "source": "e872f77b4742d477.txt", "type": "text/plain", "size": 212}, {"uid": "fcccbac8c86dbfcd", "name": "test_completed", "source": "fcccbac8c86dbfcd.png", "type": "image/png", "size": 636346}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766735778, "stop": 1753766735783, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766735783, "stop": 1753766736121, "duration": 338}, "status": "passed", "steps": [], "attachments": [{"uid": "4a7df637c32b8bb", "name": "测试总结", "source": "4a7df637c32b8bb.txt", "type": "text/plain", "size": 212}, {"uid": "afaf7d5bd543842", "name": "test_completed", "source": "afaf7d5bd543842.png", "type": "image/png", "size": 636113}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1b6a4830d523918c", "name": "stdout", "source": "1b6a4830d523918c.txt", "type": "text/plain", "size": 10778}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766736124, "stop": 1753766736124, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766736126, "stop": 1753766737425, "duration": 1299}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_yandex_eats"}, {"name": "subSuite", "value": "TestEllaYandexEats"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_yandex_eats"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1991f1f1174d50bb.json", "parameterValues": []}