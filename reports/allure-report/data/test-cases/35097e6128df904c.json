{"uid": "35097e6128df904c", "name": "测试set personal hotspot返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot.TestEllaSetPersonalHotspot#test_set_personal_hotspot", "historyId": "4f538fc772535a0c0811ad87d3aa9494", "time": {"start": 1753766129315, "stop": 1753766138465, "duration": 9150}, "description": "验证set personal hotspot指令返回预期的不支持响应", "descriptionHtml": "<p>验证set personal hotspot指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766116798, "stop": 1753766129313, "duration": 12515}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766129313, "stop": 1753766129313, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set personal hotspot指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "time": {"start": 1753766129315, "stop": 1753766138092, "duration": 8777}, "status": "passed", "steps": [{"name": "执行命令: set personal hotspot", "time": {"start": 1753766129315, "stop": 1753766137742, "duration": 8427}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766137743, "stop": 1753766138091, "duration": 348}, "status": "passed", "steps": [], "attachments": [{"uid": "5d79600202382f1", "name": "测试总结", "source": "5d79600202382f1.txt", "type": "text/plain", "size": 230}, {"uid": "e15ff305f8d5060", "name": "test_completed", "source": "e15ff305f8d5060.png", "type": "image/png", "size": 654860}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766138092, "stop": 1753766138096, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766138096, "stop": 1753766138465, "duration": 369}, "status": "passed", "steps": [], "attachments": [{"uid": "e56ad8a1b36d87e3", "name": "测试总结", "source": "e56ad8a1b36d87e3.txt", "type": "text/plain", "size": 230}, {"uid": "9de00733a0526e3c", "name": "test_completed", "source": "9de00733a0526e3c.png", "type": "image/png", "size": 654919}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "90ea2d99a35e039", "name": "stdout", "source": "90ea2d99a35e039.txt", "type": "text/plain", "size": 10526}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766138466, "stop": 1753766138466, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766138469, "stop": 1753766139731, "duration": 1262}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_personal_hotspot"}, {"name": "subSuite", "value": "TestEllaSetPersonalHotspot"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_personal_hotspot"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "35097e6128df904c.json", "parameterValues": []}