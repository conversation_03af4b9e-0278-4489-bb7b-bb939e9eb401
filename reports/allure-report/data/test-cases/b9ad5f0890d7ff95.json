{"uid": "b9ad5f0890d7ff95", "name": "测试switch to performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode.TestEllaSwitchPerformanceMode#test_switch_to_performance_mode", "historyId": "34f3c9cc9098f792051e7099b7a9fdc1", "time": {"start": 1753766497236, "stop": 1753766505493, "duration": 8257}, "description": "验证switch to performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to performance mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766484677, "stop": 1753766497235, "duration": 12558}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766497235, "stop": 1753766497235, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证switch to performance mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1753766497236, "stop": 1753766505157, "duration": 7921}, "status": "passed", "steps": [{"name": "执行命令: switch to performance mode", "time": {"start": 1753766497236, "stop": 1753766504835, "duration": 7599}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766504835, "stop": 1753766505156, "duration": 321}, "status": "passed", "steps": [], "attachments": [{"uid": "46eb6ced037ab106", "name": "测试总结", "source": "46eb6ced037ab106.txt", "type": "text/plain", "size": 247}, {"uid": "8dfd087f393b5773", "name": "test_completed", "source": "8dfd087f393b5773.png", "type": "image/png", "size": 676704}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766505157, "stop": 1753766505162, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766505162, "stop": 1753766505492, "duration": 330}, "status": "passed", "steps": [], "attachments": [{"uid": "894219303571a377", "name": "测试总结", "source": "894219303571a377.txt", "type": "text/plain", "size": 247}, {"uid": "eb5f07be8f953ede", "name": "test_completed", "source": "eb5f07be8f953ede.png", "type": "image/png", "size": 676827}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "7003c2a9205c26ae", "name": "stdout", "source": "7003c2a9205c26ae.txt", "type": "text/plain", "size": 10601}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766505495, "stop": 1753766505495, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766505497, "stop": 1753766506772, "duration": 1275}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_performance_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b9ad5f0890d7ff95.json", "parameterValues": []}