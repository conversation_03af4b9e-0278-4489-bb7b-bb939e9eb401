{"uid": "7373dd251d127d09", "name": "测试set lockscreen passwords返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords.TestEllaSetLockscreenPasswords#test_set_lockscreen_passwords", "historyId": "89b134ac1374e88187e793daf9f8fcab", "time": {"start": 1753766040123, "stop": 1753766048747, "duration": 8624}, "description": "验证set lockscreen passwords指令返回预期的不支持响应", "descriptionHtml": "<p>验证set lockscreen passwords指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766027711, "stop": 1753766040122, "duration": 12411}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766040122, "stop": 1753766040122, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set lockscreen passwords指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": 1753766040123, "stop": 1753766048413, "duration": 8290}, "status": "passed", "steps": [{"name": "执行命令: set lockscreen passwords", "time": {"start": 1753766040123, "stop": 1753766048123, "duration": 8000}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766048123, "stop": 1753766048412, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "1ea3b11a573143db", "name": "测试总结", "source": "1ea3b11a573143db.txt", "type": "text/plain", "size": 243}, {"uid": "1504693d475c43fa", "name": "test_completed", "source": "1504693d475c43fa.png", "type": "image/png", "size": 658154}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766048413, "stop": 1753766048420, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766048420, "stop": 1753766048746, "duration": 326}, "status": "passed", "steps": [], "attachments": [{"uid": "1ad114c6264c1a65", "name": "测试总结", "source": "1ad114c6264c1a65.txt", "type": "text/plain", "size": 243}, {"uid": "4e55b695ad537308", "name": "test_completed", "source": "4e55b695ad537308.png", "type": "image/png", "size": 657918}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "ebe0885d7d876515", "name": "stdout", "source": "ebe0885d7d876515.txt", "type": "text/plain", "size": 10585}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766048749, "stop": 1753766048749, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766048751, "stop": 1753766050059, "duration": 1308}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_lockscreen_passwords"}, {"name": "subSuite", "value": "TestEllaSetLockscreenPasswords"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_lockscreen_passwords"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7373dd251d127d09.json", "parameterValues": []}