{"uid": "2f290e7ec2fa59be", "name": "测试set flex-still mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode.TestEllaSetFlexStillMode#test_set_flex_still_mode", "historyId": "969451307307a13b4d89a24bb46ad0bb", "time": {"start": 1753765878872, "stop": 1753765887490, "duration": 8618}, "description": "验证set flex-still mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证set flex-still mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765866028, "stop": 1753765878871, "duration": 12843}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765878871, "stop": 1753765878871, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set flex-still mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1753765878872, "stop": 1753765887181, "duration": 8309}, "status": "passed", "steps": [{"name": "执行命令: set flex-still mode", "time": {"start": 1753765878872, "stop": 1753765886859, "duration": 7987}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765886859, "stop": 1753765887180, "duration": 321}, "status": "passed", "steps": [], "attachments": [{"uid": "3320978b37c1669a", "name": "测试总结", "source": "3320978b37c1669a.txt", "type": "text/plain", "size": 227}, {"uid": "e66ad606bcc200c6", "name": "test_completed", "source": "e66ad606bcc200c6.png", "type": "image/png", "size": 647914}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765887181, "stop": 1753765887186, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765887186, "stop": 1753765887489, "duration": 303}, "status": "passed", "steps": [], "attachments": [{"uid": "25f155ec3d31dbe7", "name": "测试总结", "source": "25f155ec3d31dbe7.txt", "type": "text/plain", "size": 227}, {"uid": "bed047a4558ce7e0", "name": "test_completed", "source": "bed047a4558ce7e0.png", "type": "image/png", "size": 647548}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "95f6a15b6091cb3d", "name": "stdout", "source": "95f6a15b6091cb3d.txt", "type": "text/plain", "size": 10510}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765887492, "stop": 1753765887492, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765887494, "stop": 1753765888728, "duration": 1234}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_flex_still_mode"}, {"name": "subSuite", "value": "TestEllaSetFlexStillMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_flex_still_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2f290e7ec2fa59be.json", "parameterValues": []}