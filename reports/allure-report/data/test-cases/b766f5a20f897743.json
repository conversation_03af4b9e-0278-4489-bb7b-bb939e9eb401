{"uid": "b766f5a20f897743", "name": "测试check battery information返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_check_battery_information.TestEllaCheckBatteryInformation#test_check_battery_information", "historyId": "613ef0933e4be87696bbedd56b4f0052", "time": {"start": 1753761631982, "stop": 1753761641998, "duration": 10016}, "description": "验证check battery information指令返回预期的不支持响应", "descriptionHtml": "<p>验证check battery information指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761619503, "stop": 1753761631981, "duration": 12478}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761631981, "stop": 1753761631981, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证check battery information指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: check battery information", "time": {"start": 1753761631982, "stop": 1753761641625, "duration": 9643}, "status": "passed", "steps": [{"name": "执行命令: check battery information", "time": {"start": 1753761631983, "stop": 1753761640642, "duration": 8659}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761640642, "stop": 1753761641623, "duration": 981}, "status": "passed", "steps": [], "attachments": [{"uid": "e333a107e5d0d1d3", "name": "测试总结", "source": "e333a107e5d0d1d3.txt", "type": "text/plain", "size": 231}, {"uid": "9cb8f4aac12c2370", "name": "test_completed", "source": "9cb8f4aac12c2370.png", "type": "image/png", "size": 656962}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761641625, "stop": 1753761641630, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761641630, "stop": 1753761641998, "duration": 368}, "status": "passed", "steps": [], "attachments": [{"uid": "9c4f04e05c103017", "name": "测试总结", "source": "9c4f04e05c103017.txt", "type": "text/plain", "size": 231}, {"uid": "aa1002eb6c1a9152", "name": "test_completed", "source": "aa1002eb6c1a9152.png", "type": "image/png", "size": 656825}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "8c68ee01dcc5db69", "name": "stdout", "source": "8c68ee01dcc5db69.txt", "type": "text/plain", "size": 11117}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761641998, "stop": 1753761641998, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761642001, "stop": 1753761643228, "duration": 1227}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "设备信息"}, {"name": "story", "value": "设备型号: TECNO CM8"}, {"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_check_battery_information"}, {"name": "subSuite", "value": "TestEllaCheckBatteryInformation"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_check_battery_information"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "b766f5a20f897743.json", "parameterValues": []}