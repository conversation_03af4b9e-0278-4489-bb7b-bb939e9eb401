{"uid": "c2c1ab04ba6a67df", "name": "测试disable auto pickup返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_auto_pickup.TestEllaDisableAutoPickup#test_disable_auto_pickup", "historyId": "891e31ec8bf99ceed4f462ce0c8629db", "time": {"start": 1753761835778, "stop": 1753761844202, "duration": 8424}, "description": "验证disable auto pickup指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable auto pickup指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761823317, "stop": 1753761835776, "duration": 12459}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761835776, "stop": 1753761835776, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable auto pickup指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable auto pickup", "time": {"start": 1753761835778, "stop": 1753761843831, "duration": 8053}, "status": "passed", "steps": [{"name": "执行命令: disable auto pickup", "time": {"start": 1753761835778, "stop": 1753761843406, "duration": 7628}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761843406, "stop": 1753761843831, "duration": 425}, "status": "passed", "steps": [], "attachments": [{"uid": "baa80e7088e2d483", "name": "测试总结", "source": "baa80e7088e2d483.txt", "type": "text/plain", "size": 225}, {"uid": "121455bf5b4a0832", "name": "test_completed", "source": "121455bf5b4a0832.png", "type": "image/png", "size": 645355}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761843831, "stop": 1753761843837, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761843837, "stop": 1753761844201, "duration": 364}, "status": "passed", "steps": [], "attachments": [{"uid": "73e490ae46103bdd", "name": "测试总结", "source": "73e490ae46103bdd.txt", "type": "text/plain", "size": 225}, {"uid": "7b3e49859b2e8d40", "name": "test_completed", "source": "7b3e49859b2e8d40.png", "type": "image/png", "size": 646404}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "a298cd1696c41228", "name": "stdout", "source": "a298cd1696c41228.txt", "type": "text/plain", "size": 10506}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761844203, "stop": 1753761844204, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761844206, "stop": 1753761845505, "duration": 1299}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaDisableAutoPickup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_auto_pickup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c2c1ab04ba6a67df.json", "parameterValues": []}