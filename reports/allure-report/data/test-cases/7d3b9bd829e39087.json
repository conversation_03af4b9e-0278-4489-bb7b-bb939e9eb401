{"uid": "7d3b9bd829e39087", "name": "测试disable network enhancement返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement.TestEllaDisableNetworkEnhancement#test_disable_network_enhancement", "historyId": "3d685d9ca6a0d7795be3c96921595318", "time": {"start": 1753764779947, "stop": 1753764788591, "duration": 8644}, "description": "验证disable network enhancement指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable network enhancement指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753764767264, "stop": 1753764779947, "duration": 12683}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753764779947, "stop": 1753764779947, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable network enhancement指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1753764779947, "stop": 1753764788265, "duration": 8318}, "status": "passed", "steps": [{"name": "执行命令: disable network enhancement", "time": {"start": 1753764779947, "stop": 1753764787949, "duration": 8002}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764787949, "stop": 1753764788264, "duration": 315}, "status": "passed", "steps": [], "attachments": [{"uid": "e14cca32ef343401", "name": "测试总结", "source": "e14cca32ef343401.txt", "type": "text/plain", "size": 248}, {"uid": "7cc24375e5592afa", "name": "test_completed", "source": "7cc24375e5592afa.png", "type": "image/png", "size": 646093}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753764788265, "stop": 1753764788270, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764788270, "stop": 1753764788590, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "b3491771c7e1a406", "name": "测试总结", "source": "b3491771c7e1a406.txt", "type": "text/plain", "size": 248}, {"uid": "e3421334f4e298a0", "name": "test_completed", "source": "e3421334f4e298a0.png", "type": "image/png", "size": 645802}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1a3721b8cb3f1ca1", "name": "stdout", "source": "1a3721b8cb3f1ca1.txt", "type": "text/plain", "size": 10606}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753764788592, "stop": 1753764788592, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753764788595, "stop": 1753764789818, "duration": 1223}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_network_enhancement"}, {"name": "subSuite", "value": "TestEllaDisableNetworkEnhancement"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_network_enhancement"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7d3b9bd829e39087.json", "parameterValues": []}