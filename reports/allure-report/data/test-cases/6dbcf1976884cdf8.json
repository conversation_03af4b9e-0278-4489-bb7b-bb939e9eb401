{"uid": "6dbcf1976884cdf8", "name": "测试set my fonts返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_my_fonts.TestEllaSetMyFonts#test_set_my_fonts", "historyId": "7d3b4e67344145885187c529ee88a9aa", "time": {"start": 1753766062593, "stop": 1753766071390, "duration": 8797}, "description": "验证set my fonts指令返回预期的不支持响应", "descriptionHtml": "<p>验证set my fonts指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766050066, "stop": 1753766062592, "duration": 12526}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766062592, "stop": 1753766062592, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set my fonts指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1753766062593, "stop": 1753766071092, "duration": 8499}, "status": "passed", "steps": [{"name": "执行命令: set my fonts", "time": {"start": 1753766062593, "stop": 1753766070760, "duration": 8167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766070761, "stop": 1753766071091, "duration": 330}, "status": "passed", "steps": [], "attachments": [{"uid": "7919f1fa61439a80", "name": "测试总结", "source": "7919f1fa61439a80.txt", "type": "text/plain", "size": 206}, {"uid": "ed62331b5a852001", "name": "test_completed", "source": "ed62331b5a852001.png", "type": "image/png", "size": 633095}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766071092, "stop": 1753766071097, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766071097, "stop": 1753766071389, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "b1f300e13ac3449e", "name": "测试总结", "source": "b1f300e13ac3449e.txt", "type": "text/plain", "size": 206}, {"uid": "7c284941a161e6b7", "name": "test_completed", "source": "7c284941a161e6b7.png", "type": "image/png", "size": 632924}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "bbb5af5d8cb2601f", "name": "stdout", "source": "bbb5af5d8cb2601f.txt", "type": "text/plain", "size": 10414}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766071391, "stop": 1753766071391, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766071394, "stop": 1753766072664, "duration": 1270}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_my_fonts"}, {"name": "subSuite", "value": "TestEllaSetMyFonts"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_my_fonts"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6dbcf1976884cdf8.json", "parameterValues": []}