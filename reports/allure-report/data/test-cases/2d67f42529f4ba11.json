{"uid": "2d67f42529f4ba11", "name": "测试set font size返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_font_size.TestEllaSetFontSize#test_set_font_size", "historyId": "6c315a350a546e1382e435255d28245b", "time": {"start": 1753765967757, "stop": 1753765976842, "duration": 9085}, "description": "验证set font size指令返回预期的不支持响应", "descriptionHtml": "<p>验证set font size指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765955232, "stop": 1753765967754, "duration": 12522}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765967755, "stop": 1753765967755, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set font size指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set font size", "time": {"start": 1753765967757, "stop": 1753765976451, "duration": 8694}, "status": "passed", "steps": [{"name": "执行命令: set font size", "time": {"start": 1753765967757, "stop": 1753765976153, "duration": 8396}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765976153, "stop": 1753765976450, "duration": 297}, "status": "passed", "steps": [], "attachments": [{"uid": "513724be3c3fa4a9", "name": "测试总结", "source": "513724be3c3fa4a9.txt", "type": "text/plain", "size": 209}, {"uid": "13639dd08e9eb7f0", "name": "test_completed", "source": "13639dd08e9eb7f0.png", "type": "image/png", "size": 640570}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765976451, "stop": 1753765976459, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765976459, "stop": 1753765976841, "duration": 382}, "status": "passed", "steps": [], "attachments": [{"uid": "3f9221b588aa50e3", "name": "测试总结", "source": "3f9221b588aa50e3.txt", "type": "text/plain", "size": 209}, {"uid": "5b31ee21ec39a9c3", "name": "test_completed", "source": "5b31ee21ec39a9c3.png", "type": "image/png", "size": 640575}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "cf00f877c11a2107", "name": "stdout", "source": "cf00f877c11a2107.txt", "type": "text/plain", "size": 10419}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765976844, "stop": 1753765976844, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765976846, "stop": 1753765978117, "duration": 1271}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_font_size"}, {"name": "subSuite", "value": "TestEllaSetFontSize"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_font_size"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2d67f42529f4ba11.json", "parameterValues": []}