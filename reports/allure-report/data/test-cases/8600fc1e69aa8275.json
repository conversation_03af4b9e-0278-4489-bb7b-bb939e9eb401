{"uid": "8600fc1e69aa8275", "name": "测试order a burger返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_order_a_burger.TestEllaOrderBurger#test_order_a_burger", "historyId": "2b244b852ff1236f560ec792596ae556", "time": {"start": 1753765574537, "stop": 1753765583369, "duration": 8832}, "description": "验证order a burger指令返回预期的不支持响应", "descriptionHtml": "<p>验证order a burger指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765562101, "stop": 1753765574536, "duration": 12435}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765574536, "stop": 1753765574536, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证order a burger指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1753765574537, "stop": 1753765583069, "duration": 8532}, "status": "passed", "steps": [{"name": "执行命令: order a burger", "time": {"start": 1753765574537, "stop": 1753765582719, "duration": 8182}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765582719, "stop": 1753765583069, "duration": 350}, "status": "passed", "steps": [], "attachments": [{"uid": "20c50ee56f25360a", "name": "测试总结", "source": "20c50ee56f25360a.txt", "type": "text/plain", "size": 212}, {"uid": "20c7bdf7073de76a", "name": "test_completed", "source": "20c7bdf7073de76a.png", "type": "image/png", "size": 642041}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765583069, "stop": 1753765583076, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765583076, "stop": 1753765583368, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "29058c407025c5ad", "name": "测试总结", "source": "29058c407025c5ad.txt", "type": "text/plain", "size": 212}, {"uid": "713e81686003fef6", "name": "test_completed", "source": "713e81686003fef6.png", "type": "image/png", "size": 641687}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "4e3d2372b37421ce", "name": "stdout", "source": "4e3d2372b37421ce.txt", "type": "text/plain", "size": 10431}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765583371, "stop": 1753765583371, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765583373, "stop": 1753765584638, "duration": 1265}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_order_a_burger"}, {"name": "subSuite", "value": "TestEllaOrderBurger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_order_a_burger"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "8600fc1e69aa8275.json", "parameterValues": []}