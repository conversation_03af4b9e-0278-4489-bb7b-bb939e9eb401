{"uid": "e3bf02bacdd2495b", "name": "测试disable accelerate dialogue返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue.TestEllaDisableAccelerateDialogue#test_disable_accelerate_dialogue", "historyId": "4cfe8e55b2a91a62bbf1141ffc0cc530", "time": {"start": 1753761790803, "stop": 1753761799572, "duration": 8769}, "description": "验证disable accelerate dialogue指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable accelerate dialogue指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761778616, "stop": 1753761790802, "duration": 12186}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761790802, "stop": 1753761790802, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable accelerate dialogue指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable accelerate dialogue", "time": {"start": 1753761790803, "stop": 1753761799196, "duration": 8393}, "status": "passed", "steps": [{"name": "执行命令: disable accelerate dialogue", "time": {"start": 1753761790803, "stop": 1753761798861, "duration": 8058}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761798861, "stop": 1753761799196, "duration": 335}, "status": "passed", "steps": [], "attachments": [{"uid": "d294c7b4585f2496", "name": "测试总结", "source": "d294c7b4585f2496.txt", "type": "text/plain", "size": 248}, {"uid": "1a21ad515ce3c242", "name": "test_completed", "source": "1a21ad515ce3c242.png", "type": "image/png", "size": 677024}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761799196, "stop": 1753761799203, "duration": 7}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761799203, "stop": 1753761799571, "duration": 368}, "status": "passed", "steps": [], "attachments": [{"uid": "a82f8a201630e7bd", "name": "测试总结", "source": "a82f8a201630e7bd.txt", "type": "text/plain", "size": 248}, {"uid": "4deba34af8ed7450", "name": "test_completed", "source": "4deba34af8ed7450.png", "type": "image/png", "size": 676822}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "c96ef4dcebd52aff", "name": "stdout", "source": "c96ef4dcebd52aff.txt", "type": "text/plain", "size": 10615}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761799573, "stop": 1753761799573, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761799576, "stop": 1753761800809, "duration": 1233}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_accelerate_dialogue"}, {"name": "subSuite", "value": "TestEllaDisableAccelerateDialogue"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_accelerate_dialogue"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "e3bf02bacdd2495b.json", "parameterValues": []}