{"uid": "7d7e356db129b7a7", "name": "测试disable all ai magic box features返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features.TestEllaDisableAllAiMagicBoxFeatures#test_disable_all_ai_magic_box_features", "historyId": "********************************", "time": {"start": 1753761812917, "stop": 1753761822063, "duration": 9146}, "description": "验证disable all ai magic box features指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable all ai magic box features指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761800816, "stop": 1753761812915, "duration": 12099}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761812915, "stop": 1753761812915, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable all ai magic box features指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable all ai magic box features", "time": {"start": 1753761812917, "stop": 1753761821686, "duration": 8769}, "status": "passed", "steps": [{"name": "执行命令: disable all ai magic box features", "time": {"start": 1753761812917, "stop": 1753761821394, "duration": 8477}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761821394, "stop": 1753761821684, "duration": 290}, "status": "passed", "steps": [], "attachments": [{"uid": "1510d2a636ba3b0", "name": "测试总结", "source": "1510d2a636ba3b0.txt", "type": "text/plain", "size": 252}, {"uid": "ff60833a835e12c8", "name": "test_completed", "source": "ff60833a835e12c8.png", "type": "image/png", "size": 665185}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761821686, "stop": 1753761821690, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761821691, "stop": 1753761822061, "duration": 370}, "status": "passed", "steps": [], "attachments": [{"uid": "7bde6d3269003f0", "name": "测试总结", "source": "7bde6d3269003f0.txt", "type": "text/plain", "size": 252}, {"uid": "f643534c03f321d5", "name": "test_completed", "source": "f643534c03f321d5.png", "type": "image/png", "size": 665272}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "6c9087878ab9136b", "name": "stdout", "source": "6c9087878ab9136b.txt", "type": "text/plain", "size": 10642}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761822064, "stop": 1753761822064, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761822066, "stop": 1753761823307, "duration": 1241}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_all_ai_magic_box_features"}, {"name": "subSuite", "value": "TestEllaDisableAllAiMagicBoxFeatures"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_all_ai_magic_box_features"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "7d7e356db129b7a7.json", "parameterValues": []}