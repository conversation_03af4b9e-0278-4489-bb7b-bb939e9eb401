{"uid": "246bdbd532071352", "name": "测试turn on high brightness mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode.TestEllaTurnHighBrightnessMode#test_turn_on_high_brightness_mode", "historyId": "599b7a465f619c38a4638073f59c38c0", "time": {"start": 1753766654111, "stop": 1753766662935, "duration": 8824}, "description": "验证turn on high brightness mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证turn on high brightness mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766641407, "stop": 1753766654111, "duration": 12704}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766654111, "stop": 1753766654111, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证turn on high brightness mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1753766654111, "stop": 1753766662577, "duration": 8466}, "status": "passed", "steps": [{"name": "执行命令: turn on high brightness mode", "time": {"start": 1753766654111, "stop": 1753766662278, "duration": 8167}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766662278, "stop": 1753766662576, "duration": 298}, "status": "passed", "steps": [], "attachments": [{"uid": "8472c6204765c5b5", "name": "测试总结", "source": "8472c6204765c5b5.txt", "type": "text/plain", "size": 250}, {"uid": "2df26c38801a3cb3", "name": "test_completed", "source": "2df26c38801a3cb3.png", "type": "image/png", "size": 669623}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766662577, "stop": 1753766662582, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766662582, "stop": 1753766662934, "duration": 352}, "status": "passed", "steps": [], "attachments": [{"uid": "ddf5acc7e8a0d1d5", "name": "测试总结", "source": "ddf5acc7e8a0d1d5.txt", "type": "text/plain", "size": 250}, {"uid": "198f1e73b832cdb8", "name": "test_completed", "source": "198f1e73b832cdb8.png", "type": "image/png", "size": 669407}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "92490f573ab306c9", "name": "stdout", "source": "92490f573ab306c9.txt", "type": "text/plain", "size": 10609}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766662936, "stop": 1753766662936, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766662939, "stop": 1753766664189, "duration": 1250}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_turn_on_high_brightness_mode"}, {"name": "subSuite", "value": "TestEllaTurnHighBrightnessMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_turn_on_high_brightness_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "246bdbd532071352.json", "parameterValues": []}