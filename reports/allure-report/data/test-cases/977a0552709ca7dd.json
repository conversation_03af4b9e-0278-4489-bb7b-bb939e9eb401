{"uid": "977a0552709ca7dd", "name": "测试close equilibrium mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_close_equilibrium_mode.TestEllaCloseEquilibriumMode#test_close_equilibrium_mode", "historyId": "dc901cadfe1de0042de7c0f7461a804e", "time": {"start": 1753761723925, "stop": 1753761733203, "duration": 9278}, "description": "验证close equilibrium mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证close equilibrium mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761711269, "stop": 1753761723924, "duration": 12655}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761723924, "stop": 1753761723924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证close equilibrium mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: close equilibrium mode", "time": {"start": 1753761723925, "stop": 1753761732818, "duration": 8893}, "status": "passed", "steps": [{"name": "执行命令: close equilibrium mode", "time": {"start": 1753761723925, "stop": 1753761732430, "duration": 8505}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761732430, "stop": 1753761732816, "duration": 386}, "status": "passed", "steps": [], "attachments": [{"uid": "d8fa8852e3b99d73", "name": "测试总结", "source": "d8fa8852e3b99d73.txt", "type": "text/plain", "size": 237}, {"uid": "16c29cdee0d2df84", "name": "test_completed", "source": "16c29cdee0d2df84.png", "type": "image/png", "size": 661232}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761732818, "stop": 1753761732823, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761732823, "stop": 1753761733202, "duration": 379}, "status": "passed", "steps": [], "attachments": [{"uid": "f00db36e1f9f5f54", "name": "测试总结", "source": "f00db36e1f9f5f54.txt", "type": "text/plain", "size": 237}, {"uid": "29dcc31b5d4b5ff6", "name": "test_completed", "source": "29dcc31b5d4b5ff6.png", "type": "image/png", "size": 661204}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "7479a8b64347b9e7", "name": "stdout", "source": "7479a8b64347b9e7.txt", "type": "text/plain", "size": 10548}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761733204, "stop": 1753761733204, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761733206, "stop": 1753761734469, "duration": 1263}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_equilibrium_mode"}, {"name": "subSuite", "value": "TestEllaCloseEquilibriumMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_equilibrium_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "977a0552709ca7dd.json", "parameterValues": []}