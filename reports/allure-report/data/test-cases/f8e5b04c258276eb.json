{"uid": "f8e5b04c258276eb", "name": "测试driving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_driving_mode.TestEllaDrivingMode#test_driving_mode", "historyId": "3215de286c6ddd59d6e52a44f2a9967d", "time": {"start": 1753764915935, "stop": 1753764924701, "duration": 8766}, "description": "验证driving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证driving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753764903833, "stop": 1753764915934, "duration": 12101}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753764915934, "stop": 1753764915934, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证driving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1753764915936, "stop": 1753764924361, "duration": 8425}, "status": "passed", "steps": [{"name": "执行命令: driving mode", "time": {"start": 1753764915936, "stop": 1753764924040, "duration": 8104}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764924040, "stop": 1753764924360, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "1e59700c2a19bb07", "name": "测试总结", "source": "1e59700c2a19bb07.txt", "type": "text/plain", "size": 214}, {"uid": "acb2d6545a7f3e72", "name": "test_completed", "source": "acb2d6545a7f3e72.png", "type": "image/png", "size": 608274}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753764924361, "stop": 1753764924365, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764924365, "stop": 1753764924700, "duration": 335}, "status": "passed", "steps": [], "attachments": [{"uid": "cb8bd3933a39de4e", "name": "测试总结", "source": "cb8bd3933a39de4e.txt", "type": "text/plain", "size": 214}, {"uid": "a2bac65ce4af04c8", "name": "test_completed", "source": "a2bac65ce4af04c8.png", "type": "image/png", "size": 608327}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "f69b0997ef116dd", "name": "stdout", "source": "f69b0997ef116dd.txt", "type": "text/plain", "size": 10440}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753764924704, "stop": 1753764924704, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753764924706, "stop": 1753764925920, "duration": 1214}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_driving_mode"}, {"name": "subSuite", "value": "TestEllaDrivingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_driving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f8e5b04c258276eb.json", "parameterValues": []}