{"uid": "6d09de9067475c54", "name": "测试set battery saver settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings.TestEllaSetBatterySaverSettings#test_set_battery_saver_settings", "historyId": "e60dab4e55edacbecf632d4d22f368e6", "time": {"start": 1753765690785, "stop": 1753765703376, "duration": 12591}, "description": "验证set battery saver settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证set battery saver settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765678439, "stop": 1753765690784, "duration": 12345}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765690784, "stop": 1753765690784, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set battery saver settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1753765690785, "stop": 1753765703023, "duration": 12238}, "status": "passed", "steps": [{"name": "执行命令: set battery saver settings", "time": {"start": 1753765690785, "stop": 1753765702659, "duration": 11874}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765702659, "stop": 1753765703022, "duration": 363}, "status": "passed", "steps": [], "attachments": [{"uid": "b12e7d95b513dcb8", "name": "测试总结", "source": "b12e7d95b513dcb8.txt", "type": "text/plain", "size": 239}, {"uid": "bc3b7003792182a0", "name": "test_completed", "source": "bc3b7003792182a0.png", "type": "image/png", "size": 640391}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765703023, "stop": 1753765703029, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765703029, "stop": 1753765703375, "duration": 346}, "status": "passed", "steps": [], "attachments": [{"uid": "e65a21d8817f2dcd", "name": "测试总结", "source": "e65a21d8817f2dcd.txt", "type": "text/plain", "size": 239}, {"uid": "2daea7002cf72f3a", "name": "test_completed", "source": "2daea7002cf72f3a.png", "type": "image/png", "size": 640391}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "a71ff41084fb3f9a", "name": "stdout", "source": "a71ff41084fb3f9a.txt", "type": "text/plain", "size": 11059}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765703377, "stop": 1753765703377, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765703379, "stop": 1753765704622, "duration": 1243}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_battery_saver_settings"}, {"name": "subSuite", "value": "TestEllaSetBatterySaverSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_battery_saver_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "6d09de9067475c54.json", "parameterValues": []}