{"uid": "cb7dced3ed5dce81", "name": "测试disable call rejection返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_call_rejection.TestEllaDisableCallRejection#test_disable_call_rejection", "historyId": "1ca8d9c300d55584cdcf637ede08bdba", "time": {"start": 1753761880191, "stop": 1753761893514, "duration": 13323}, "description": "验证disable call rejection指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable call rejection指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761867789, "stop": 1753761880191, "duration": 12402}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761880191, "stop": 1753761880191, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable call rejection指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable call rejection", "time": {"start": 1753761880192, "stop": 1753761893188, "duration": 12996}, "status": "passed", "steps": [{"name": "执行命令: disable call rejection", "time": {"start": 1753761880192, "stop": 1753761892867, "duration": 12675}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761892867, "stop": 1753761893187, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "a012b7f303bc0210", "name": "测试总结", "source": "a012b7f303bc0210.txt", "type": "text/plain", "size": 234}, {"uid": "7a5ddde7b8e98783", "name": "test_completed", "source": "7a5ddde7b8e98783.png", "type": "image/png", "size": 648479}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761893188, "stop": 1753761893193, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761893193, "stop": 1753761893514, "duration": 321}, "status": "passed", "steps": [], "attachments": [{"uid": "ca96ba36f5a3e2dc", "name": "测试总结", "source": "ca96ba36f5a3e2dc.txt", "type": "text/plain", "size": 234}, {"uid": "1aeb5f39c31e886a", "name": "test_completed", "source": "1aeb5f39c31e886a.png", "type": "image/png", "size": 648446}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "e87db8fa8de059b4", "name": "stdout", "source": "e87db8fa8de059b4.txt", "type": "text/plain", "size": 11497}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761893516, "stop": 1753761893516, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761893519, "stop": 1753761894765, "duration": 1246}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_call_rejection"}, {"name": "subSuite", "value": "TestEllaDisableCallRejection"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_call_rejection"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "cb7dced3ed5dce81.json", "parameterValues": []}