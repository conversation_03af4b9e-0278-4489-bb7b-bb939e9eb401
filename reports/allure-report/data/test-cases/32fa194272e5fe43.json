{"uid": "32fa194272e5fe43", "name": "测试jump to battery and power saving返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving.TestEllaJumpBatteryPowerSaving#test_jump_to_battery_and_power_saving", "historyId": "436193bc4e8c44d21b6520da0589f88d", "time": {"start": 1753765345752, "stop": 1753765354649, "duration": 8897}, "description": "验证jump to battery and power saving指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to battery and power saving指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765333425, "stop": 1753765345751, "duration": 12326}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765345751, "stop": 1753765345751, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证jump to battery and power saving指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1753765345752, "stop": 1753765354349, "duration": 8597}, "status": "passed", "steps": [{"name": "执行命令: jump to battery and power saving", "time": {"start": 1753765345752, "stop": 1753765354019, "duration": 8267}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765354019, "stop": 1753765354347, "duration": 328}, "status": "passed", "steps": [], "attachments": [{"uid": "e6f51cc3668775b5", "name": "测试总结", "source": "e6f51cc3668775b5.txt", "type": "text/plain", "size": 260}, {"uid": "ae936e203f96f32c", "name": "test_completed", "source": "ae936e203f96f32c.png", "type": "image/png", "size": 635340}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765354349, "stop": 1753765354357, "duration": 8}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765354357, "stop": 1753765354649, "duration": 292}, "status": "passed", "steps": [], "attachments": [{"uid": "e7cff86aa7b29c5", "name": "测试总结", "source": "e7cff86aa7b29c5.txt", "type": "text/plain", "size": 260}, {"uid": "fc6e3733a3a1d3c7", "name": "test_completed", "source": "fc6e3733a3a1d3c7.png", "type": "image/png", "size": 635393}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "db0bd60d2b2182c8", "name": "stdout", "source": "db0bd60d2b2182c8.txt", "type": "text/plain", "size": 10651}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765354650, "stop": 1753765354651, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765354653, "stop": 1753765355918, "duration": 1265}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_battery_and_power_saving"}, {"name": "subSuite", "value": "TestEllaJumpBatteryPowerSaving"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_battery_and_power_saving"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "32fa194272e5fe43.json", "parameterValues": []}