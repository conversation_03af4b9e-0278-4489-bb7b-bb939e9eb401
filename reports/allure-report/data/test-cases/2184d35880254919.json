{"uid": "2184d35880254919", "name": "测试disable magic voice changer返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer.TestEllaDisableMagicVoiceChanger#test_disable_magic_voice_changer", "historyId": "503ff57584874e8387e6b367bfa70c8c", "time": {"start": 1753764758200, "stop": 1753764765984, "duration": 7784}, "description": "验证disable magic voice changer指令返回预期的不支持响应", "descriptionHtml": "<p>验证disable magic voice changer指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761917444, "stop": 1753764758199, "duration": 2840755}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753764758199, "stop": 1753764758199, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证disable magic voice changer指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753764758200, "stop": 1753764765676, "duration": 7476}, "status": "passed", "steps": [{"name": "执行命令: disable magic voice changer", "time": {"start": 1753764758200, "stop": 1753764765385, "duration": 7185}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764765385, "stop": 1753764765674, "duration": 289}, "status": "passed", "steps": [], "attachments": [{"uid": "2c22d05bd331161b", "name": "测试总结", "source": "2c22d05bd331161b.txt", "type": "text/plain", "size": 244}, {"uid": "d8372d2a987437c8", "name": "test_completed", "source": "d8372d2a987437c8.png", "type": "image/png", "size": 667776}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753764765676, "stop": 1753764765680, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764765680, "stop": 1753764765984, "duration": 304}, "status": "passed", "steps": [], "attachments": [{"uid": "f12bc8db6bf82668", "name": "测试总结", "source": "f12bc8db6bf82668.txt", "type": "text/plain", "size": 244}, {"uid": "6ba234170ecd0d1e", "name": "test_completed", "source": "6ba234170ecd0d1e.png", "type": "image/png", "size": 668453}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "c65639dae2d98417", "name": "stdout", "source": "c65639dae2d98417.txt", "type": "text/plain", "size": 10601}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753764765985, "stop": 1753764765985, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753764765987, "stop": 1753764767253, "duration": 1266}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_disable_magic_voice_changer"}, {"name": "subSuite", "value": "TestEllaDisableMagicVoiceChanger"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_disable_magic_voice_changer"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "2184d35880254919.json", "parameterValues": []}