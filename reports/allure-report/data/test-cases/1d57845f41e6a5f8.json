{"uid": "1d57845f41e6a5f8", "name": "测试set screen to minimum brightness返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness.TestEllaSetScreenMinimumBrightness#test_set_screen_to_minimum_brightness", "historyId": "544fc8b021d2dbcaf295cd05b798f816", "time": {"start": 1753766292819, "stop": 1753766301429, "duration": 8610}, "description": "验证set screen to minimum brightness指令返回预期的不支持响应", "descriptionHtml": "<p>验证set screen to minimum brightness指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766280368, "stop": 1753766292819, "duration": 12451}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766292819, "stop": 1753766292819, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set screen to minimum brightness指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1753766292819, "stop": 1753766301086, "duration": 8267}, "status": "passed", "steps": [{"name": "执行命令: set screen to minimum brightness", "time": {"start": 1753766292820, "stop": 1753766300753, "duration": 7933}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766300753, "stop": 1753766301084, "duration": 331}, "status": "passed", "steps": [], "attachments": [{"uid": "4fa1119d54c83340", "name": "测试总结", "source": "4fa1119d54c83340.txt", "type": "text/plain", "size": 247}, {"uid": "aa6c65048b337085", "name": "test_completed", "source": "aa6c65048b337085.png", "type": "image/png", "size": 624511}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766301086, "stop": 1753766301089, "duration": 3}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766301089, "stop": 1753766301428, "duration": 339}, "status": "passed", "steps": [], "attachments": [{"uid": "aa0f44de74ff6321", "name": "测试总结", "source": "aa0f44de74ff6321.txt", "type": "text/plain", "size": 247}, {"uid": "c05261b3ec02eb85", "name": "test_completed", "source": "c05261b3ec02eb85.png", "type": "image/png", "size": 624339}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1a5571ddfc3f0077", "name": "stdout", "source": "1a5571ddfc3f0077.txt", "type": "text/plain", "size": 10620}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766301431, "stop": 1753766301431, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766301433, "stop": 1753766302707, "duration": 1274}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_screen_to_minimum_brightness"}, {"name": "subSuite", "value": "TestEllaSetScreenMinimumBrightness"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_screen_to_minimum_brightness"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1d57845f41e6a5f8.json", "parameterValues": []}