{"uid": "1b1585fb19777980", "name": "测试Voice setting page返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_voice_setting_page.TestEllaVoiceSettingPage#test_voice_setting_page", "historyId": "9c301cfc137fb94f119957b5f74291ec", "time": {"start": 1753766700113, "stop": 1753766712922, "duration": 12809}, "description": "验证Voice setting page指令返回预期的不支持响应", "descriptionHtml": "<p>验证Voice setting page指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766687285, "stop": 1753766700110, "duration": 12825}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766700110, "stop": 1753766700110, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证Voice setting page指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1753766700113, "stop": 1753766712593, "duration": 12480}, "status": "passed", "steps": [{"name": "执行命令: Voice setting page", "time": {"start": 1753766700113, "stop": 1753766712187, "duration": 12074}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766712187, "stop": 1753766712590, "duration": 403}, "status": "passed", "steps": [], "attachments": [{"uid": "610b2f801e56d092", "name": "测试总结", "source": "610b2f801e56d092.txt", "type": "text/plain", "size": 215}, {"uid": "56b07a7bd3107185", "name": "test_completed", "source": "56b07a7bd3107185.png", "type": "image/png", "size": 590799}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766712593, "stop": 1753766712597, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766712597, "stop": 1753766712921, "duration": 324}, "status": "passed", "steps": [], "attachments": [{"uid": "33a4598a70631191", "name": "测试总结", "source": "33a4598a70631191.txt", "type": "text/plain", "size": 215}, {"uid": "de9a178c741eac53", "name": "test_completed", "source": "de9a178c741eac53.png", "type": "image/png", "size": 591128}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "60d0f817eaf7947a", "name": "stdout", "source": "60d0f817eaf7947a.txt", "type": "text/plain", "size": 10971}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766712924, "stop": 1753766712924, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766712927, "stop": 1753766714264, "duration": 1337}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_voice_setting_page"}, {"name": "subSuite", "value": "TestEllaVoiceSettingPage"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_voice_setting_page"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "1b1585fb19777980.json", "parameterValues": []}