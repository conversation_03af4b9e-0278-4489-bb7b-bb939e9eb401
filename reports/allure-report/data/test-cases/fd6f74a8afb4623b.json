{"uid": "fd6f74a8afb4623b", "name": "测试jump to call notifications返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications.TestEllaJumpCallNotifications#test_jump_to_call_notifications", "historyId": "540cff5d6d552c22ec37f66efd17315f", "time": {"start": 1753765390724, "stop": 1753765403631, "duration": 12907}, "description": "验证jump to call notifications指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to call notifications指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765378412, "stop": 1753765390723, "duration": 12311}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765390723, "stop": 1753765390723, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证jump to call notifications指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "time": {"start": 1753765390724, "stop": 1753765403289, "duration": 12565}, "status": "passed", "steps": [{"name": "执行命令: jump to call notifications", "time": {"start": 1753765390724, "stop": 1753765402925, "duration": 12201}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765402925, "stop": 1753765403288, "duration": 363}, "status": "passed", "steps": [], "attachments": [{"uid": "f6152d24fefe6ab6", "name": "测试总结", "source": "f6152d24fefe6ab6.txt", "type": "text/plain", "size": 246}, {"uid": "e2dae2a6c311ec26", "name": "test_completed", "source": "e2dae2a6c311ec26.png", "type": "image/png", "size": 638768}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765403289, "stop": 1753765403293, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765403293, "stop": 1753765403630, "duration": 337}, "status": "passed", "steps": [], "attachments": [{"uid": "e11b025a6c33304f", "name": "测试总结", "source": "e11b025a6c33304f.txt", "type": "text/plain", "size": 246}, {"uid": "456f0c50f584afb3", "name": "test_completed", "source": "456f0c50f584afb3.png", "type": "image/png", "size": 639180}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "e92ff1cafcf401e5", "name": "stdout", "source": "e92ff1cafcf401e5.txt", "type": "text/plain", "size": 11547}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765403634, "stop": 1753765403634, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765403636, "stop": 1753765404959, "duration": 1323}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_call_notifications"}, {"name": "subSuite", "value": "TestEllaJumpCallNotifications"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_call_notifications"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "fd6f74a8afb4623b.json", "parameterValues": []}