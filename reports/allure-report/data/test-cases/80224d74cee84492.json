{"uid": "80224d74cee84492", "name": "测试Enable Call Rejection返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_call_rejection.TestEllaEnableCallRejection#test_enable_call_rejection", "historyId": "ff8d76af98b9fdfaa206acbf87daa843", "time": {"start": 1753765053607, "stop": 1753765066572, "duration": 12965}, "description": "验证Enable Call Rejection指令返回预期的不支持响应", "descriptionHtml": "<p>验证Enable Call Rejection指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765040549, "stop": 1753765053606, "duration": 13057}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765053606, "stop": 1753765053606, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证Enable Call Rejection指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1753765053607, "stop": 1753765066259, "duration": 12652}, "status": "passed", "steps": [{"name": "执行命令: Enable Call Rejection", "time": {"start": 1753765053607, "stop": 1753765065974, "duration": 12367}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765065974, "stop": 1753765066257, "duration": 283}, "status": "passed", "steps": [], "attachments": [{"uid": "fec5d83922f404f2", "name": "测试总结", "source": "fec5d83922f404f2.txt", "type": "text/plain", "size": 232}, {"uid": "f12f6828275242ab", "name": "test_completed", "source": "f12f6828275242ab.png", "type": "image/png", "size": 649213}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765066259, "stop": 1753765066263, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765066263, "stop": 1753765066571, "duration": 308}, "status": "passed", "steps": [], "attachments": [{"uid": "a25537614b2585ba", "name": "测试总结", "source": "a25537614b2585ba.txt", "type": "text/plain", "size": 232}, {"uid": "3ecd7814c0dc2ea8", "name": "test_completed", "source": "3ecd7814c0dc2ea8.png", "type": "image/png", "size": 649714}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1c97a1e2d1801034", "name": "stdout", "source": "1c97a1e2d1801034.txt", "type": "text/plain", "size": 11477}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765066573, "stop": 1753765066573, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765066576, "stop": 1753765067894, "duration": 1318}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_call_rejection"}, {"name": "subSuite", "value": "TestEllaEnableCallRejection"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_call_rejection"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "80224d74cee84492.json", "parameterValues": []}