{"uid": "509ae9b5f4ed11e1", "name": "测试switch to power saving mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode.TestEllaSwitchPowerSavingMode#test_switch_to_power_saving_mode", "historyId": "a0efcebc4cee6024e690bd290b4f3fbb", "time": {"start": 1753766519465, "stop": 1753766528222, "duration": 8757}, "description": "验证switch to power saving mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证switch to power saving mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753766506782, "stop": 1753766519464, "duration": 12682}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753766519464, "stop": 1753766519464, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证switch to power saving mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1753766519465, "stop": 1753766527897, "duration": 8432}, "status": "passed", "steps": [{"name": "执行命令: switch to power saving mode", "time": {"start": 1753766519465, "stop": 1753766527640, "duration": 8175}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766527640, "stop": 1753766527896, "duration": 256}, "status": "passed", "steps": [], "attachments": [{"uid": "6a0e67223b40a9d3", "name": "测试总结", "source": "6a0e67223b40a9d3.txt", "type": "text/plain", "size": 251}, {"uid": "9c05f810b466b161", "name": "test_completed", "source": "9c05f810b466b161.png", "type": "image/png", "size": 655755}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753766527897, "stop": 1753766527901, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753766527901, "stop": 1753766528221, "duration": 320}, "status": "passed", "steps": [], "attachments": [{"uid": "507911e019fab79b", "name": "测试总结", "source": "507911e019fab79b.txt", "type": "text/plain", "size": 251}, {"uid": "8442ad1c426b0daf", "name": "test_completed", "source": "8442ad1c426b0daf.png", "type": "image/png", "size": 655587}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "1856e434a89d6a9b", "name": "stdout", "source": "1856e434a89d6a9b.txt", "type": "text/plain", "size": 10607}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753766528224, "stop": 1753766528224, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753766528226, "stop": 1753766529529, "duration": 1303}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_switch_to_power_saving_mode"}, {"name": "subSuite", "value": "TestEllaSwitchPowerSavingMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_switch_to_power_saving_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "509ae9b5f4ed11e1.json", "parameterValues": []}