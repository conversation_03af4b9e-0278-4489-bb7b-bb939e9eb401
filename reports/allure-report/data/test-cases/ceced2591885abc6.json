{"uid": "ceced2591885abc6", "name": "测试jump to high brightness mode settings返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings.TestEllaJumpHighBrightnessModeSettings#test_jump_to_high_brightness_mode_settings", "historyId": "48a2a80bfed06f0c82b99a0aaa26e252", "time": {"start": 1753765417843, "stop": 1753765430253, "duration": 12410}, "description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "descriptionHtml": "<p>验证jump to high brightness mode settings指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765404967, "stop": 1753765417842, "duration": 12875}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765417842, "stop": 1753765417842, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证jump to high brightness mode settings指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1753765417843, "stop": 1753765429892, "duration": 12049}, "status": "passed", "steps": [{"name": "执行命令: jump to high brightness mode settings", "time": {"start": 1753765417843, "stop": 1753765429506, "duration": 11663}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765429506, "stop": 1753765429890, "duration": 384}, "status": "passed", "steps": [], "attachments": [{"uid": "c158189675e03d10", "name": "测试总结", "source": "c158189675e03d10.txt", "type": "text/plain", "size": 268}, {"uid": "f6aaf0916ee7be94", "name": "test_completed", "source": "f6aaf0916ee7be94.png", "type": "image/png", "size": 677910}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765429892, "stop": 1753765429896, "duration": 4}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765429896, "stop": 1753765430253, "duration": 357}, "status": "passed", "steps": [], "attachments": [{"uid": "e7bb888c5891a639", "name": "测试总结", "source": "e7bb888c5891a639.txt", "type": "text/plain", "size": 268}, {"uid": "61bdeb762ea0823f", "name": "test_completed", "source": "61bdeb762ea0823f.png", "type": "image/png", "size": 677910}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "f7d97a1ca911f702", "name": "stdout", "source": "f7d97a1ca911f702.txt", "type": "text/plain", "size": 11193}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765430255, "stop": 1753765430255, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765430257, "stop": 1753765431516, "duration": 1259}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_jump_to_high_brightness_mode_settings"}, {"name": "subSuite", "value": "TestEllaJumpHighBrightnessModeSettings"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_jump_to_high_brightness_mode_settings"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "ceced2591885abc6.json", "parameterValues": []}