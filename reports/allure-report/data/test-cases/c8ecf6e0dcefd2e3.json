{"uid": "c8ecf6e0dcefd2e3", "name": "测试how to set screenshots返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots.TestEllaHowSetScreenshots#test_how_to_set_screenshots", "historyId": "bfd4a9e37b70dca0b14b0ccf5246fc4a", "time": {"start": 1753765219280, "stop": 1753765227782, "duration": 8502}, "description": "验证how to set screenshots指令返回预期的不支持响应", "descriptionHtml": "<p>验证how to set screenshots指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765206761, "stop": 1753765219278, "duration": 12517}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765219278, "stop": 1753765219279, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证how to set screenshots指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1753765219280, "stop": 1753765227448, "duration": 8168}, "status": "passed", "steps": [{"name": "执行命令: how to set screenshots", "time": {"start": 1753765219280, "stop": 1753765227145, "duration": 7865}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765227145, "stop": 1753765227447, "duration": 302}, "status": "passed", "steps": [], "attachments": [{"uid": "89478c7391382fba", "name": "测试总结", "source": "89478c7391382fba.txt", "type": "text/plain", "size": 229}, {"uid": "e6004a993eebed1b", "name": "test_completed", "source": "e6004a993eebed1b.png", "type": "image/png", "size": 650246}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765227448, "stop": 1753765227453, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765227453, "stop": 1753765227781, "duration": 328}, "status": "passed", "steps": [], "attachments": [{"uid": "7ef9ee10f793dff", "name": "测试总结", "source": "7ef9ee10f793dff.txt", "type": "text/plain", "size": 229}, {"uid": "1681d55ce816a201", "name": "test_completed", "source": "1681d55ce816a201.png", "type": "image/png", "size": 650027}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "14c951a4b7dc94c7", "name": "stdout", "source": "14c951a4b7dc94c7.txt", "type": "text/plain", "size": 10527}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765227783, "stop": 1753765227784, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765227785, "stop": 1753765229054, "duration": 1269}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_how_to_set_screenshots"}, {"name": "subSuite", "value": "TestEllaHowSetScreenshots"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_how_to_set_screenshots"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "c8ecf6e0dcefd2e3.json", "parameterValues": []}