{"uid": "f5a5bd26d1bd1074", "name": "测试close performance mode返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_close_performance_mode.TestEllaClosePerformanceMode#test_close_performance_mode", "historyId": "57c053de6acd628d4b4cd1230b702a40", "time": {"start": 1753761746394, "stop": 1753761755312, "duration": 8918}, "description": "验证close performance mode指令返回预期的不支持响应", "descriptionHtml": "<p>验证close performance mode指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753761734476, "stop": 1753761746393, "duration": 11917}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753761746393, "stop": 1753761746393, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证close performance mode指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: close performance mode", "time": {"start": 1753761746394, "stop": 1753761754930, "duration": 8536}, "status": "passed", "steps": [{"name": "执行命令: close performance mode", "time": {"start": 1753761746394, "stop": 1753761754540, "duration": 8146}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761754540, "stop": 1753761754929, "duration": 389}, "status": "passed", "steps": [], "attachments": [{"uid": "de1ac9633007cc4e", "name": "测试总结", "source": "de1ac9633007cc4e.txt", "type": "text/plain", "size": 227}, {"uid": "1fa015b439da138e", "name": "test_completed", "source": "1fa015b439da138e.png", "type": "image/png", "size": 648093}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753761754930, "stop": 1753761754936, "duration": 6}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753761754936, "stop": 1753761755310, "duration": 374}, "status": "passed", "steps": [], "attachments": [{"uid": "b435ea2c776d68b5", "name": "测试总结", "source": "b435ea2c776d68b5.txt", "type": "text/plain", "size": 227}, {"uid": "2cba5547170f939", "name": "test_completed", "source": "2cba5547170f939.png", "type": "image/png", "size": 647997}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "fefb3c1f6c794400", "name": "stdout", "source": "fefb3c1f6c794400.txt", "type": "text/plain", "size": 10527}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753761755314, "stop": 1753761755314, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753761755317, "stop": 1753761756547, "duration": 1230}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_close_performance_mode"}, {"name": "subSuite", "value": "TestEllaClosePerformanceMode"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_close_performance_mode"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "f5a5bd26d1bd1074.json", "parameterValues": []}