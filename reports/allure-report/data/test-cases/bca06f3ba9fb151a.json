{"uid": "bca06f3ba9fb151a", "name": "测试enable auto pickup返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup.TestEllaEnableAutoPickup#test_enable_auto_pickup", "historyId": "57acf2797af332487c1fdb9a53a30e4f", "time": {"start": 1753764982371, "stop": 1753764991328, "duration": 8957}, "description": "验证enable auto pickup指令返回预期的不支持响应", "descriptionHtml": "<p>验证enable auto pickup指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753764970181, "stop": 1753764982369, "duration": 12188}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753764982369, "stop": 1753764982370, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证enable auto pickup指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1753764982371, "stop": 1753764990970, "duration": 8599}, "status": "passed", "steps": [{"name": "执行命令: enable auto pickup", "time": {"start": 1753764982371, "stop": 1753764990673, "duration": 8302}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764990673, "stop": 1753764990969, "duration": 296}, "status": "passed", "steps": [], "attachments": [{"uid": "f7fa3fccf19babe", "name": "测试总结", "source": "f7fa3fccf19babe.txt", "type": "text/plain", "size": 234}, {"uid": "f46d44d655e4febe", "name": "test_completed", "source": "f46d44d655e4febe.png", "type": "image/png", "size": 639896}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753764990970, "stop": 1753764990975, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753764990975, "stop": 1753764991327, "duration": 352}, "status": "passed", "steps": [], "attachments": [{"uid": "fb59d37e013acd6d", "name": "测试总结", "source": "fb59d37e013acd6d.txt", "type": "text/plain", "size": 234}, {"uid": "42c222c22ec62727", "name": "test_completed", "source": "42c222c22ec62727.png", "type": "image/png", "size": 639958}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "ceb72711c85fcfff", "name": "stdout", "source": "ceb72711c85fcfff.txt", "type": "text/plain", "size": 10528}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753764991329, "stop": 1753764991329, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753764991332, "stop": 1753764992553, "duration": 1221}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_enable_auto_pickup"}, {"name": "subSuite", "value": "TestEllaEnableAutoPickup"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_enable_auto_pickup"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "bca06f3ba9fb151a.json", "parameterValues": []}