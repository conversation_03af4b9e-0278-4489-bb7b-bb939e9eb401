{"uid": "55306a20cf18accf", "name": "测试set app auto rotate返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate.TestEllaSetAppAutoRotate#test_set_app_auto_rotate", "historyId": "eda16efd838471b84f33f12ec91662c9", "time": {"start": 1753765645878, "stop": 1753765655018, "duration": 9140}, "description": "验证set app auto rotate指令返回预期的不支持响应", "descriptionHtml": "<p>验证set app auto rotate指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765633535, "stop": 1753765645878, "duration": 12343}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765645878, "stop": 1753765645878, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set app auto rotate指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set app auto rotate", "time": {"start": 1753765645878, "stop": 1753765654686, "duration": 8808}, "status": "passed", "steps": [{"name": "执行命令: set app auto rotate", "time": {"start": 1753765645878, "stop": 1753765654352, "duration": 8474}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765654352, "stop": 1753765654685, "duration": 333}, "status": "passed", "steps": [], "attachments": [{"uid": "c73a31d4381fd7c9", "name": "测试总结", "source": "c73a31d4381fd7c9.txt", "type": "text/plain", "size": 227}, {"uid": "6075b0aa070f7906", "name": "test_completed", "source": "6075b0aa070f7906.png", "type": "image/png", "size": 636986}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765654686, "stop": 1753765654691, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765654691, "stop": 1753765655017, "duration": 326}, "status": "passed", "steps": [], "attachments": [{"uid": "9d1a3359ce178c1d", "name": "测试总结", "source": "9d1a3359ce178c1d.txt", "type": "text/plain", "size": 227}, {"uid": "9bf19802a0cbdf81", "name": "test_completed", "source": "9bf19802a0cbdf81.png", "type": "image/png", "size": 636983}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "475fa60416234894", "name": "stdout", "source": "475fa60416234894.txt", "type": "text/plain", "size": 10501}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765655020, "stop": 1753765655020, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765655022, "stop": 1753765656308, "duration": 1286}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_app_auto_rotate"}, {"name": "subSuite", "value": "TestEllaSetAppAutoRotate"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_app_auto_rotate"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "55306a20cf18accf.json", "parameterValues": []}