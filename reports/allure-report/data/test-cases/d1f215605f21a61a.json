{"uid": "d1f215605f21a61a", "name": "测试set floating windows返回正确的不支持响应", "fullName": "testcases.test_ella.unsupported_commands.test_set_floating_windows.TestEllaSetFloatingWindows#test_set_floating_windows", "historyId": "ff945a5d436679bddd13261b231955ec", "time": {"start": 1753765923195, "stop": 1753765931855, "duration": 8660}, "description": "验证set floating windows指令返回预期的不支持响应", "descriptionHtml": "<p>验证set floating windows指令返回预期的不支持响应</p>\n", "status": "passed", "flaky": false, "newFailed": false, "newBroken": false, "newPassed": false, "retriesCount": 0, "retriesStatusChange": false, "beforeStages": [{"name": "setup_test_environment", "time": {"start": 1753761619311, "stop": 1753761619503, "duration": 192}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app", "time": {"start": 1753765910747, "stop": 1753765923195, "duration": 12448}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "take_screenshot_on_failure", "time": {"start": 1753765923195, "stop": 1753765923195, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "testStage": {"description": "验证set floating windows指令返回预期的不支持响应", "status": "passed", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1753765923195, "stop": 1753765931529, "duration": 8334}, "status": "passed", "steps": [{"name": "执行命令: set floating windows", "time": {"start": 1753765923195, "stop": 1753765931236, "duration": 8041}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765931236, "stop": 1753765931527, "duration": 291}, "status": "passed", "steps": [], "attachments": [{"uid": "897022ff0ed6e632", "name": "测试总结", "source": "897022ff0ed6e632.txt", "type": "text/plain", "size": 230}, {"uid": "d3c1830434d97d67", "name": "test_completed", "source": "d3c1830434d97d67.png", "type": "image/png", "size": 635673}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 2, "hasContent": true, "attachmentStep": false}, {"name": "验证响应包含期望的不支持内容", "time": {"start": 1753765931529, "stop": 1753765931534, "duration": 5}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "记录测试结果", "time": {"start": 1753765931534, "stop": 1753765931855, "duration": 321}, "status": "passed", "steps": [], "attachments": [{"uid": "76f4a4860581be10", "name": "测试总结", "source": "76f4a4860581be10.txt", "type": "text/plain", "size": 230}, {"uid": "fbb5ff09b3492952", "name": "test_completed", "source": "fbb5ff09b3492952.png", "type": "image/png", "size": 635317}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 2, "stepsCount": 0, "hasContent": true, "attachmentStep": false}], "attachments": [{"uid": "da864a5a58189d91", "name": "stdout", "source": "da864a5a58189d91.txt", "type": "text/plain", "size": 10526}], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 5, "stepsCount": 5, "hasContent": true, "attachmentStep": false}, "afterStages": [{"name": "take_screenshot_on_failure::0", "time": {"start": 1753765931857, "stop": 1753765931857, "duration": 0}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "ella_app::0", "time": {"start": 1753765931859, "stop": 1753765933104, "duration": 1245}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}, {"name": "setup_test_environment::0", "time": {"start": 1753766737427, "stop": 1753766737428, "duration": 1}, "status": "passed", "steps": [], "attachments": [], "parameters": [], "shouldDisplayMessage": false, "attachmentsCount": 0, "stepsCount": 0, "hasContent": false, "attachmentStep": false}], "labels": [{"name": "feature", "value": "Ella语音助手"}, {"name": "story", "value": "不支持的指令"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "testcases.test_ella.unsupported_commands"}, {"name": "suite", "value": "test_set_floating_windows"}, {"name": "subSuite", "value": "TestEllaSetFloatingWindows"}, {"name": "host", "value": "SHCYbucy-pc"}, {"name": "thread", "value": "36044-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "testcases.test_ella.unsupported_commands.test_set_floating_windows"}, {"name": "resultFormat", "value": "allure2"}], "parameters": [], "links": [], "hidden": false, "retry": false, "extra": {"severity": "normal", "retries": [], "categories": [], "tags": ["smoke"]}, "source": "d1f215605f21a61a.json", "parameterValues": []}